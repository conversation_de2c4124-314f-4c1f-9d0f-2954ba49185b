html {
    background-color: #181b1d;
    background-size: 32px 32px;
    background-image: radial-gradient(circle, #3d454b 1px, rgba(0, 0, 0, 0) 1px);
}

#left-col {
    position: fixed;
    left: 0;
    top:0;
    bottom:0;
    width: 460px;
    z-index: 2;
    transition: left 0.3s ease-out;
    background-color: #222A30;
    overflow-y: auto;
}

#content {
    position: relative;
	padding: 30px 0 0 0;
	margin-left: 0;
	transition: margin 0.2s cubic-bezier(.4,0,.2,1);
}
@media screen and (min-width: 960px){
	#content {
		/* equal to left-col width */
		margin-left: 460px;
	}
}

#main-seed {
    width: 120px;
    text-align: right;
    margin-right: 10px;
}

#main-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 3;
    border-radius: 0;
    height: 7px;
    background-color: #0c3047;
    border: 0;
}

#main-progress::-moz-progress-bar {
    background: #00c070;
}

#main-progress::-webkit-progress-bar {
    background: transparent;
}

#main-progress::-webkit-progress-value {
    background: #00c070;
}

#left-col h4 {
    background-color: #13669e;
    position: relative;
    padding: 8px 0;
}

#sidebar-toggle {
    position: absolute;
    z-index: 0;
    top: -14px;
    right: 10px;
}

.left-content-box {
    background-color: #222A30;
}

#results {
    padding: 40px 20px;
}

#results img {
    border-radius: 8px;
    box-shadow: 2px 2px 10px 0 rgba(0, 0, 0, 0.8);
    object-fit: contain;
    transition: all 0.3s ease;
}

/* 图像容器样式 */
#results {
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

/* 响应式图像显示 */
@media screen and (max-width: 768px) {
    #results img {
        max-width: 95vw !important;
        max-height: 70vh !important;
    }
}

@media screen and (min-width: 769px) and (max-width: 1200px) {
    #results img {
        max-width: 80vw !important;
        max-height: 80vh !important;
    }
}

@media screen and (min-width: 1201px) {
    #results img {
        max-width: 70vw !important;
        max-height: 85vh !important;
    }
}

.uk-notification-message {
    background-color: #06633c;
    color: #ddd;
}

datalist {
    display: flex;
    justify-content: space-between;
    font-size: .875rem;
    flex-direction: row;
    padding: 0 4px;
}

datalist option {
    padding: 0;
}

.uk-accordion > div {
    margin-top: 0!important;
}

.uk-accordion-content {
    padding: 15px;
}

.uk-accordion-title {
    padding: 10px 12px;
    border-bottom: 1px solid #394650;
    background-color: #171d21;
}

/* 覆盖UIkit的grid样式，解决图片左侧空白问题 */
.uk-grid>* {
    /* padding-left: 30px; */
    padding-left: 0 !important;
}

/* 历史图片面板样式 */
.history-panel {
    background-color: #222A30;
    border-left: 1px solid #394650;
    height: 100vh;
    padding: 20px 15px;
    overflow-y: auto;
}

.history-panel h4 {
    background-color: #13669e;
    padding: 8px 0;
    margin-bottom: 20px;
    border-radius: 4px;
}

.history-images-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.history-image-item {
    background-color: #171d21;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #394650;
    transition: all 0.3s ease;
}

.history-image-item:hover {
    border-color: #13669e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(19, 102, 158, 0.3);
}

.history-image-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-image-item img:hover {
    transform: scale(1.05);
}

.history-image-info {
    margin-top: 8px;
    font-size: 0.8rem;
    color: #aaa;
    text-align: center;
}

.history-image-time {
    font-size: 0.7rem;
    color: #666;
    text-align: center;
    margin-top: 4px;
}

/* 移动端隐藏历史面板 */
@media screen and (max-width: 959px) {
    .history-panel {
        display: none;
    }
}

/* 历史图片详情模态框样式 */
.uk-modal-dialog {
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    height: auto;
    max-height: 95vh;
}

.uk-modal-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    flex-shrink: 0; /* 防止header被压缩 */
}

.uk-modal-body {
    padding: 0; /* 移除内边距，由grid控制 */
    flex-grow: 1; /* 占据剩余空间 */
    overflow-y: auto; /* 内容溢出时滚动 */
    min-height: 0; /* 解决flex item中height:100%子元素溢出问题 */
}

.uk-modal-large .uk-modal-body > .uk-grid {
    height: 100%;
}

.image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
}

.uk-modal-large .uk-modal-body > .uk-grid > .uk-width-2-3@m {
    background-color: #f0f0f0;
    padding: 20px;
    border-radius: 8px;
}

.image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
}

.info-panel {
    background: #ffffff; /* 保持与模态框背景一致 */
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* 内容多时从顶部开始排列 */
    border-left: 1px solid #e9ecef;
    overflow-y: auto; /* 当内容溢出时，仅在此面板内显示滚动条 */
}

.info-panel-content {
    margin: auto 0; /* 当内容未溢出时，实现垂直居中 */
}

.info-panel h3 {
    color: #13669e;
    margin-bottom: 25px;
    font-size: 1.3rem;
    font-weight: 600;
}

.parameter-item {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.parameter-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.parameter-label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.parameter-value {
    color: #6c757d;
    font-size: 0.9rem;
    word-break: break-all;
}

.parameter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.tag {
    background: #13669e;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    white-space: nowrap;
}

/* 响应式适配 - 大屏幕 */
@media screen and (min-width: 1200px) {
    .uk-modal-large {
        width: 80vw;
        max-width: 1400px;
    }
    
    .image-container {
        min-height: 500px;
        max-height: 75vh;
    }
    
    .info-panel {
        padding: 25px;
    }
    
    .info-panel h3 {
        font-size: 1.4rem;
    }
    
    .parameter-label {
        font-size: 1rem;
    }
    
    .parameter-value {
        font-size: 1rem;
    }
    
    .tag {
        font-size: 0.9rem;
        padding: 3px 10px;
    }
}

/* 响应式适配 - 中等屏幕 */
@media screen and (min-width: 769px) and (max-width: 1199px) {
    .uk-modal-large {
        max-width: 90vw;
        max-height: 92vh;
    }
    
    .image-container {
        min-height: 400px;
        max-height: 70vh;
    }
    
    .info-panel {
        padding: 20px;
    }
}

/* 响应式适配 - 小屏幕 */
@media screen and (max-width: 768px) {
    .uk-modal-large {
        max-width: 98vw;
        max-height: 98vh;
        margin: 1vh auto;
    }
    
    .uk-grid-large {
        margin-left: 0;
    }
    
    .uk-width-2-3@m,
    .uk-width-1-3@m {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .image-container {
        min-height: 250px;
        max-height: 60vh;
    }
    
    .info-panel {
        margin-top: 15px;
        padding: 15px;
    }
    
    .info-panel h3 {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }
    
    .parameter-item {
        margin-bottom: 12px;
        padding-bottom: 8px;
    }
    
    .parameter-label {
        font-size: 0.85rem;
    }
    
    .parameter-value {
        font-size: 0.85rem;
    }
    
    .parameter-tags {
        gap: 3px;
    }
    
    .tag {
        font-size: 0.75rem;
        padding: 1px 6px;
    }
}

/* 响应式适配 - 超小屏幕 */
@media screen and (max-width: 480px) {
    .uk-modal-large {
        max-width: 100vw;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
    }
    
    .image-container {
        min-height: 200px;
        max-height: 50vh;
    }
    
    .info-panel {
        padding: 12px;
    }
    
    .info-panel h3 {
        font-size: 1rem;
        margin-bottom: 12px;
    }
    
    .parameter-item {
        margin-bottom: 10px;
        padding-bottom: 6px;
    }
    
    .parameter-label {
        font-size: 0.8rem;
    }
    
    .parameter-value {
        font-size: 0.8rem;
    }
    
    .tag {
        font-size: 0.7rem;
        padding: 1px 4px;
    }
}

/* 响应式适配 */
@media (max-width: 959px) {
    .uk-modal-body > .uk-grid > div {
        width: 100%;
    }

    .image-container {
        height: 50vh; /* 在小屏幕上给图片一个固定的高度 */
        padding: 15px;
    }

    .info-panel {
        border-left: none;
        border-top: 1px solid #e9ecef;
        padding: 20px;
    }
}
