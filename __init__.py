from .dungeon import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS

import os
import json
import server
from aiohttp import web
from .history_api import get_history_manager

WEBROOT = os.path.join(os.path.dirname(os.path.realpath(__file__)), "web")
HISTORY_FILE = os.path.join(os.path.dirname(os.path.realpath(__file__)), "history_records.json")

@server.PromptServer.instance.routes.get("/dungeon")
def deungeon_entrance(request):
    return web.FileResponse(os.path.join(WEBROOT, "index.html"))

# 历史记录API端点
@server.PromptServer.instance.routes.get("/dungeon/api/history")
async def get_history(request):
    """获取历史记录"""
    try:
        manager = get_history_manager()
        history = manager.load_history()
        return web.json_response({
            "success": True,
            "data": history,
            "count": len(history)
        })
    except Exception as e:
        return web.json_response({
            "success": False,
            "error": str(e)
        }, status=500)

@server.PromptServer.instance.routes.post("/dungeon/api/history")
async def add_history(request):
    """添加历史记录"""
    try:
        data = await request.json()
        manager = get_history_manager()
        success = manager.add_record(data)

        if success:
            return web.json_response({
                "success": True,
                "message": "历史记录添加成功"
            })
        else:
            return web.json_response({
                "success": False,
                "error": "添加历史记录失败"
            }, status=500)
    except Exception as e:
        return web.json_response({
            "success": False,
            "error": str(e)
        }, status=500)

@server.PromptServer.instance.routes.delete("/dungeon/api/history")
async def clear_history(request):
    """清空历史记录"""
    try:
        manager = get_history_manager()
        success = manager.clear_history()

        if success:
            return web.json_response({
                "success": True,
                "message": "历史记录已清空"
            })
        else:
            return web.json_response({
                "success": False,
                "error": "清空历史记录失败"
            }, status=500)
    except Exception as e:
        return web.json_response({
            "success": False,
            "error": str(e)
        }, status=500)

@server.PromptServer.instance.routes.get("/dungeon/api/history/status")
async def get_history_status(request):
    """获取历史记录状态"""
    try:
        manager = get_history_manager()
        status = manager.get_history_info()
        return web.json_response({
            "success": True,
            "data": status
        })
    except Exception as e:
        return web.json_response({
            "success": False,
            "error": str(e)
        }, status=500)

server.PromptServer.instance.routes.static("/dungeon/css/", path=os.path.join(WEBROOT, "css"))
server.PromptServer.instance.routes.static("/dungeon/js/", path=os.path.join(WEBROOT, "js"))

__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS']
