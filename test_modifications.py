#!/usr/bin/env python3
"""
测试修改后的未来城市生成器功能
"""

import json
import os

def test_html_modifications():
    """测试HTML修改"""
    html_path = "web/index.html"
    
    if not os.path.exists(html_path):
        print(f"❌ HTML文件不存在: {html_path}")
        return False
    
    try:
        with open(html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了数量限制
        if "选择3个" in content:
            print("❌ HTML中仍包含数量限制")
            return False
        
        if "选择1个" in content:
            print("❌ HTML中仍包含数量限制")
            return False
        
        # 检查是否改为复选框
        if "atmosphere-radio" in content:
            print("❌ 氛围光照仍使用单选框")
            return False
        
        if "atmosphere-checkbox" not in content:
            print("❌ 氛围光照未改为复选框")
            return False
        
        print("✅ HTML修改正确")
        return True
        
    except Exception as e:
        print(f"❌ HTML测试错误: {e}")
        return False

def test_javascript_modifications():
    """测试JavaScript修改"""
    js_path = "web/js/dungeon.js"
    
    if not os.path.exists(js_path):
        print(f"❌ JavaScript文件不存在: {js_path}")
        return False
    
    try:
        with open(js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了数量限制
        if "最多只能选择3个" in content:
            print("❌ JavaScript中仍包含数量限制")
            return False
        
        # 检查验证逻辑
        if "请选择3个" in content:
            print("❌ JavaScript验证逻辑未更新")
            return False
        
        if "请至少选择1个" not in content:
            print("❌ JavaScript验证逻辑未正确更新")
            return False
        
        # 检查图像显示优化
        if "maxHeight" not in content:
            print("❌ 图像显示未优化")
            return False
        
        if "borderRadius" not in content:
            print("❌ 图像样式未优化")
            return False
        
        print("✅ JavaScript修改正确")
        return True
        
    except Exception as e:
        print(f"❌ JavaScript测试错误: {e}")
        return False

def test_python_modifications():
    """测试Python节点修改"""
    py_path = "dungeon.py"
    
    if not os.path.exists(py_path):
        print(f"❌ Python文件不存在: {py_path}")
        return False
    
    try:
        with open(py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了数量限制
        if "确保只选择3个" in content:
            print("❌ Python中仍包含数量限制")
            return False
        
        if "确保只选择1个" in content:
            print("❌ Python中仍包含数量限制")
            return False
        
        if "移除数量限制" not in content:
            print("❌ Python中未添加移除限制的注释")
            return False
        
        print("✅ Python修改正确")
        return True
        
    except Exception as e:
        print(f"❌ Python测试错误: {e}")
        return False

def test_documentation_modifications():
    """测试文档修改"""
    readme_path = "README.md"
    usage_path = "使用说明.md"
    
    try:
        # 测试README
        with open(readme_path, 'r', encoding='utf-8') as f:
            readme_content = f.read()
        
        if "选择3个" in readme_content:
            print("❌ README中仍包含数量限制")
            return False
        
        if "选择1个" in readme_content:
            print("❌ README中仍包含数量限制")
            return False
        
        # 测试使用说明
        with open(usage_path, 'r', encoding='utf-8') as f:
            usage_content = f.read()
        
        if "选择3个" in usage_content:
            print("❌ 使用说明中仍包含数量限制")
            return False
        
        # 检查是否包含旧的限制文本（排除正常的"至少选择1个"）
        if "选择3个" in usage_content:
            print("❌ 使用说明中仍包含数量限制")
            return False
        
        if "选择1个氛围光照" in usage_content:
            print("❌ 使用说明中仍包含数量限制")
            return False
        
        if "请至少选择1个" not in usage_content:
            print("❌ 使用说明中未更新错误信息")
            return False
        
        print("✅ 文档修改正确")
        return True
        
    except Exception as e:
        print(f"❌ 文档测试错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修改后的未来城市生成器...")
    print("=" * 60)
    
    tests = [
        ("HTML界面修改", test_html_modifications),
        ("JavaScript逻辑修改", test_javascript_modifications),
        ("Python节点修改", test_python_modifications),
        ("文档更新", test_documentation_modifications),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修改测试通过！")
        print("\n📝 修改总结:")
        print("✅ 移除了未来科技选择的3个限制")
        print("✅ 移除了未来城市选择的3个限制")
        print("✅ 移除了氛围光照选择的1个限制")
        print("✅ 将氛围光照从单选框改为复选框")
        print("✅ 优化了图像显示，适配浏览器")
        print("✅ 更新了所有相关文档")
    else:
        print("⚠️  部分修改测试失败，请检查上述错误信息。")
    
    return passed == total

if __name__ == "__main__":
    main() 