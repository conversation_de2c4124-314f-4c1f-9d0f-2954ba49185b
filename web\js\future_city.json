{"413": {"inputs": {"samples": ["571", 0], "vae": ["576", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "415": {"inputs": {"filename_prefix": "FutureCity/image_", "images": ["413", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "503": {"inputs": {"clip_l": ["568", 0], "t5xxl": ["568", 0], "guidance": 3.5, "speak_and_recognation": {"__value__": [false, true]}, "clip": ["575", 0]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIP文本编码Flux"}}, "546": {"inputs": {"String": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "KepStringLiteral", "_meta": {"title": "字符串"}}, "566": {"inputs": {"String": ["585", 0], "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "KepStringLiteral", "_meta": {"title": "字符串"}}, "568": {"inputs": {"text_1": ["546", 0], "text_2": ["566", 0], "text_3": "", "text_4": ["586", 0]}, "class_type": "LayerUtility: Text<PERSON>oin", "_meta": {"title": "文本合并"}}, "571": {"inputs": {"seed": 595557172935589, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["577", 0], "positive": ["503", 0], "negative": ["572", 0], "latent_image": ["597", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "572": {"inputs": {"conditioning": ["503", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "573": {"inputs": {"model_path": "svdq-int4_r32-flux.1-dev.safetensors", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "575": {"inputs": {"model_type": "flux.1", "text_encoder1": "clip_l.safetensors", "text_encoder2": "awq-int4-flux.1-t5xxl.safetensors", "t5_min_length": 512}, "class_type": "NunchakuTextEncoderLoaderV2", "_meta": {"title": "Nunchaku Text Encoder Loader V2"}}, "576": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "577": {"inputs": {"lora_name": "F.1未来都市_TWO.safetensors", "lora_strength": 0.8000000000000002, "model": ["573", 0]}, "class_type": "NunchakuFluxLoraLoader", "_meta": {"title": "Nunchaku FLUX.1 LoRA Loader"}}, "578": {"inputs": {"model": "qwen-plus", "prompt": ["583", 0], "max_tokens": 1024, "temperature": 0.7, "top_p": 0.7, "seed": 955284618866401, "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Qwen<PERSON><PERSON>", "_meta": {"title": "🍭Qwen AI"}}, "580": {"inputs": {"text_a": ["584", 0], "text_b": ["593", 0], "text_c": ["595", 0], "text_d": ["598", 0]}, "class_type": "Text List", "_meta": {"title": "文本列表"}}, "583": {"inputs": {"delimiter": ", ", "text_list": ["580", 0]}, "class_type": "Text List to Text", "_meta": {"title": "文本列表到文本"}}, "584": {"inputs": {"String": "将下列文本组合成为一个图像英文版描述，并添加适合的背景描述，不要输出评论", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "KepStringLiteral", "_meta": {"title": "字符串"}}, "585": {"inputs": {"text_0": "A futuristic zero-carbon city under warm sunlight, featuring a carbon-neutral factory, vertical farms integrated into high-rise buildings, and solar panels covering rooftops. Smart delivery drones fly through the sky, while aerial transport pods travel along designated routes. Intelligent streetlights line eco-friendly streets, and a medical teleportation capsule is stationed outside a high-tech healthcare facility, symbolizing advanced urban living in harmony with nature.", "text": ["578", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "展示文本"}}, "586": {"inputs": {"String": "The scene is dynamic, futuristic, and evokes a sense of exploration and adventure.", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "KepStringLiteral", "_meta": {"title": "字符串"}}, "593": {"inputs": {"碳中和工厂": true, "飞行出租车": false, "磁悬浮列车": false, "屋顶花园": false, "快递无人机": true, "互动喷泉": false, "智能社区": false, "摩天农场": false, "自动驾驶巴士": false, "生态住宅": false, "太阳能屋顶": false, "风力发电塔": false, "智能路灯": true, "回收机器人": false, "雨水花园": false, "城市森林": false, "悬浮监控球": false, "磁悬浮滑板": false, "全息政务厅": false, "医疗传送舱": true}, "class_type": "FutureTechNode", "_meta": {"title": "有什么？选择3个词"}}, "595": {"inputs": {"零碳城市": true, "空中出行": true, "地面交通": false, "城市绿化": false, "无人机配送": false, "智慧广场": false, "智慧居民区": false, "垂直农场": true, "智能交通": false, "绿色建筑": false, "太阳能板": true, "风力发电": false, "智能照明": false, "垃圾分类": false, "雨水收集": false, "生态公园": false, "智能监控": false, "共享出行": false, "数字政务": false, "智慧医疗": true}, "class_type": "FutureCityNode", "_meta": {"title": "还有什么？选3个词"}}, "597": {"inputs": {"width": 800, "height": 1400, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent"}}, "598": {"inputs": {"温暖阳光": true, "柔和月光": false, "霓虹灯光": false, "黄昏余晖": false, "晨光熹微": false, "神秘阴影": false, "梦幻光晕": false, "科技蓝光": false, "金色夕阳": false, "柔和阴影": false, "强烈对比": false, "柔和漫射": false, "深邃黑暗": false, "明亮通透": false, "朦胧雾气": false, "闪烁星光": false, "温暖烛光": false, "冷峻白光": false, "柔和暖光": false, "神秘紫光": false}, "class_type": "AtmosphereNode", "_meta": {"title": "早晨还是晚上？什么光？"}}}