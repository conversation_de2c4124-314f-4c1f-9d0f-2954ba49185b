<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .warning {
            color: orange;
        }
    </style>
</head>
<body>
    <h1>历史记录功能调试</h1>
    
    <div class="debug-section">
        <h2>步骤1: 检查localStorage基础功能</h2>
        <button onclick="testLocalStorage()">测试localStorage</button>
        <div id="localStorage-output" class="output"></div>
    </div>
    
    <div class="debug-section">
        <h2>步骤2: 测试Dungeon历史记录存储</h2>
        <button onclick="createTestHistory()">创建测试历史记录</button>
        <button onclick="loadTestHistory()">加载测试历史记录</button>
        <button onclick="clearTestHistory()">清空测试历史记录</button>
        <div id="history-output" class="output"></div>
    </div>
    
    <div class="debug-section">
        <h2>步骤3: 检查DOM元素</h2>
        <button onclick="checkDOMElements()">检查DOM元素</button>
        <div id="dom-output" class="output"></div>
        
        <!-- 模拟主页面的历史面板结构 -->
        <div style="margin-top: 20px; border: 1px solid #ccc; padding: 10px;">
            <h3>模拟历史面板</h3>
            <div class="history-panel">
                <div class="history-header">
                    <h4 class="uk-text-center uk-margin-small">历史图片</h4>
                    <button id="clear-history" type="button">清空</button>
                </div>
                <div id="history-images" class="history-images-container">
                    <!-- 历史图片将在这里显示 -->
                </div>
            </div>
        </div>
    </div>
    
    <div class="debug-section">
        <h2>步骤4: 模拟主页面加载流程</h2>
        <button onclick="simulatePageLoad()">模拟页面加载</button>
        <button onclick="manualUpdateDisplay()">手动更新显示</button>
        <div id="simulation-output" class="output"></div>
    </div>

    <script>
        const STORAGE_KEY = 'dungeon_image_history';
        const MAX_HISTORY = 5;
        let imageHistory = [];
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        function testLocalStorage() {
            const output = 'localStorage-output';
            document.getElementById(output).innerHTML = '';
            
            try {
                // 测试基础功能
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                log(output, `基础存储测试: ${value === 'value' ? '成功' : '失败'}`, value === 'value' ? 'success' : 'error');
                
                // 测试JSON存储
                const testObj = { test: true, time: new Date().toISOString() };
                localStorage.setItem('test_json', JSON.stringify(testObj));
                const parsed = JSON.parse(localStorage.getItem('test_json'));
                log(output, `JSON存储测试: ${parsed.test ? '成功' : '失败'}`, parsed.test ? 'success' : 'error');
                
                // 检查现有的Dungeon数据
                const dungeonData = localStorage.getItem(STORAGE_KEY);
                log(output, `Dungeon历史记录存在: ${dungeonData ? '是' : '否'}`, dungeonData ? 'success' : 'warning');
                
                if (dungeonData) {
                    try {
                        const parsed = JSON.parse(dungeonData);
                        log(output, `Dungeon历史记录数量: ${parsed.length}`, 'info');
                        log(output, `数据内容: ${JSON.stringify(parsed, null, 2)}`, 'info');
                    } catch (e) {
                        log(output, `Dungeon数据解析失败: ${e.message}`, 'error');
                    }
                }
                
                // 清理测试数据
                localStorage.removeItem('test');
                localStorage.removeItem('test_json');
                
            } catch (error) {
                log(output, `localStorage测试失败: ${error.message}`, 'error');
            }
        }
        
        function createTestHistory() {
            const output = 'history-output';
            
            try {
                const testHistory = [];
                for (let i = 0; i < 3; i++) {
                    testHistory.push({
                        id: Date.now() + i,
                        imageUrl: `data:image/svg+xml;base64,${btoa(`<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#${Math.floor(Math.random()*16777215).toString(16)}"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white">测试${i+1}</text></svg>`)}`,
                        timestamp: new Date(Date.now() - i * 60000).toISOString(), // 每个相差1分钟
                        seed: 12345 + i,
                        futureTech: [`科技${i+1}`],
                        futureCity: [`城市${i+1}`],
                        atmosphere: [`氛围${i+1}`]
                    });
                }
                
                localStorage.setItem(STORAGE_KEY, JSON.stringify(testHistory));
                log(output, `创建测试历史记录成功，数量: ${testHistory.length}`, 'success');
                log(output, `存储的数据: ${JSON.stringify(testHistory, null, 2)}`, 'info');
                
            } catch (error) {
                log(output, `创建测试历史记录失败: ${error.message}`, 'error');
            }
        }
        
        function loadTestHistory() {
            const output = 'history-output';
            
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                log(output, `从localStorage读取数据: ${stored ? '成功' : '失败'}`, stored ? 'success' : 'error');
                
                if (stored) {
                    const parsed = JSON.parse(stored);
                    log(output, `解析JSON成功，数量: ${parsed.length}`, 'success');
                    
                    // 模拟主页面的处理逻辑
                    imageHistory = parsed.map(item => ({
                        ...item,
                        timestamp: new Date(item.timestamp)
                    })).slice(0, MAX_HISTORY);
                    
                    log(output, `处理后的历史记录数量: ${imageHistory.length}`, 'success');
                    
                    imageHistory.forEach((item, index) => {
                        log(output, `记录${index + 1}: ID=${item.id}, 种子=${item.seed}, 时间=${item.timestamp.toLocaleString()}`, 'info');
                    });
                    
                } else {
                    log(output, '没有找到历史记录数据', 'warning');
                }
                
            } catch (error) {
                log(output, `加载测试历史记录失败: ${error.message}`, 'error');
            }
        }
        
        function clearTestHistory() {
            localStorage.removeItem(STORAGE_KEY);
            imageHistory = [];
            document.getElementById('history-output').innerHTML = '';
            log('history-output', '测试历史记录已清空', 'success');
        }
        
        function checkDOMElements() {
            const output = 'dom-output';
            document.getElementById(output).innerHTML = '';
            
            // 检查各种选择器
            const selectors = [
                '#history-images',
                '.history-images-container',
                '.history-header h4',
                '.history-panel h4',
                '#clear-history'
            ];
            
            selectors.forEach(selector => {
                const element = document.querySelector(selector);
                log(output, `选择器 "${selector}": ${element ? '找到' : '未找到'}`, element ? 'success' : 'error');
                if (element) {
                    log(output, `  -> 元素类型: ${element.tagName}, ID: ${element.id}, 类名: ${element.className}`, 'info');
                }
            });
        }
        
        function simulatePageLoad() {
            const output = 'simulation-output';
            document.getElementById(output).innerHTML = '';
            
            log(output, '开始模拟页面加载流程...', 'info');
            
            // 模拟DOMContentLoaded
            setTimeout(() => {
                log(output, 'DOMContentLoaded事件触发', 'info');
                
                setTimeout(() => {
                    log(output, '延迟200ms后开始加载历史记录', 'info');
                    loadHistoryFromStorage();
                }, 200);
                
            }, 100);
            
            // 模拟window.load
            setTimeout(() => {
                log(output, 'window.load事件触发', 'info');
                
                setTimeout(() => {
                    if (imageHistory.length === 0) {
                        log(output, '备用加载: 历史记录为空，尝试重新加载', 'warning');
                        loadHistoryFromStorage();
                    } else {
                        log(output, `备用加载: 历史记录已存在 (${imageHistory.length}条)，跳过`, 'info');
                    }
                }, 300);
                
            }, 500);
        }
        
        function loadHistoryFromStorage() {
            const output = 'simulation-output';
            
            try {
                log(output, '执行 loadHistoryFromStorage()', 'info');
                
                const stored = localStorage.getItem(STORAGE_KEY);
                log(output, `localStorage.getItem结果: ${stored ? '有数据' : '无数据'}`, stored ? 'success' : 'warning');
                
                if (stored) {
                    const parsedHistory = JSON.parse(stored);
                    log(output, `JSON.parse成功，原始数量: ${parsedHistory.length}`, 'success');
                    
                    imageHistory = parsedHistory.map(item => ({
                        ...item,
                        timestamp: new Date(item.timestamp)
                    })).slice(0, MAX_HISTORY);
                    
                    log(output, `处理后数量: ${imageHistory.length}`, 'success');
                    
                    updateHistoryDisplay();
                } else {
                    log(output, '无历史数据，显示空状态', 'info');
                    updateHistoryDisplay();
                }
                
            } catch (error) {
                log(output, `loadHistoryFromStorage失败: ${error.message}`, 'error');
                imageHistory = [];
                updateHistoryDisplay();
            }
        }
        
        function updateHistoryDisplay() {
            const output = 'simulation-output';
            
            log(output, '执行 updateHistoryDisplay()', 'info');
            
            const historyImages = document.getElementById('history-images');
            log(output, `找到historyImages元素: ${historyImages ? '是' : '否'}`, historyImages ? 'success' : 'error');
            
            if (!historyImages) {
                log(output, '错误: 找不到历史图片容器，退出', 'error');
                return;
            }
            
            historyImages.innerHTML = '';
            log(output, '清空历史容器内容', 'info');
            
            const historyTitle = document.querySelector('.history-header h4');
            log(output, `找到标题元素: ${historyTitle ? '是' : '否'}`, historyTitle ? 'success' : 'error');
            
            if (historyTitle) {
                historyTitle.textContent = `历史图片 (${imageHistory.length}/${MAX_HISTORY})`;
                log(output, `更新标题: ${historyTitle.textContent}`, 'success');
            }
            
            if (imageHistory.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.innerHTML = '<p style="text-align: center; color: #999;">暂无历史图片</p>';
                historyImages.appendChild(emptyMessage);
                log(output, '显示空状态提示', 'info');
                return;
            }
            
            imageHistory.forEach((item, index) => {
                const historyItem = document.createElement('div');
                historyItem.style.cssText = 'border: 1px solid #ccc; margin: 5px; padding: 10px; border-radius: 4px;';
                
                const img = document.createElement('img');
                img.src = item.imageUrl;
                img.style.cssText = 'width: 100px; height: 100px; object-fit: cover;';
                
                const info = document.createElement('div');
                info.textContent = `种子: ${item.seed}`;
                
                const time = document.createElement('div');
                const now = new Date();
                const diffMs = now - item.timestamp;
                const diffMins = Math.floor(diffMs / 60000);
                
                if (diffMins < 1) {
                    time.textContent = '刚刚';
                } else if (diffMins < 60) {
                    time.textContent = `${diffMins}分钟前`;
                } else {
                    time.textContent = item.timestamp.toLocaleDateString();
                }
                
                historyItem.appendChild(img);
                historyItem.appendChild(info);
                historyItem.appendChild(time);
                historyImages.appendChild(historyItem);
                
                log(output, `添加历史项目 ${index + 1}: 种子=${item.seed}`, 'success');
            });
        }
        
        function manualUpdateDisplay() {
            updateHistoryDisplay();
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                testLocalStorage();
            }, 100);
        });
    </script>
</body>
</html>
