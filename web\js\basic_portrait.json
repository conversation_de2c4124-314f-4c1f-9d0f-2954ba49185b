{"1": {"inputs": {"ckpt_name": ""}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "4": {"inputs": {"width": 1024, "height": 1024, "size_cond_factor": 4, "text": "", "clip": ["1", 1]}, "class_type": "CLIPTextEncodeSDXL+", "_meta": {"title": "🔧 SDXLCLIPTextEncode"}}, "5": {"inputs": {"width": 1024, "height": 1024, "size_cond_factor": 4, "text": "", "clip": ["1", 1]}, "class_type": "CLIPTextEncodeSDXL+", "_meta": {"title": "🔧 SDXLCLIPTextEncode"}}, "6": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "7": {"inputs": {"seed": 0, "steps": 25, "cfg": 7, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["1", 0], "positive": ["4", 0], "negative": ["5", 0], "latent_image": ["6", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "8": {"inputs": {"samples": ["7", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "13": {"inputs": {"filename_prefix": "Dungeon", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}