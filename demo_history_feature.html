<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史图片功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 3fr 1fr;
            gap: 20px;
            min-height: 600px;
        }
        .main-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .history-panel {
            background: #222A30;
            border-radius: 8px;
            padding: 15px;
            color: white;
        }
        .history-panel h4 {
            background: #13669e;
            padding: 8px 0;
            margin: 0 0 20px 0;
            border-radius: 4px;
            text-align: center;
        }
        .history-images-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .history-image-item {
            background: #171d21;
            border-radius: 8px;
            padding: 10px;
            border: 1px solid #394650;
            transition: all 0.3s ease;
        }
        .history-image-item:hover {
            border-color: #13669e;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(19, 102, 158, 0.3);
        }
        .history-image-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
        }
        .history-image-info {
            margin-top: 8px;
            font-size: 0.8rem;
            color: #aaa;
            text-align: center;
        }
        .history-image-time {
            font-size: 0.7rem;
            color: #666;
            text-align: center;
            margin-top: 4px;
        }
        .feature-list {
            margin-top: 30px;
            padding: 20px;
            background: #e9ecef;
            border-radius: 8px;
        }
        .feature-list h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .feature-list ul {
            color: #6c757d;
            line-height: 1.6;
        }
        .feature-list li {
            margin-bottom: 8px;
        }
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            .history-panel {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">未来城市生成器 - 历史图片功能演示</h1>
        
        <div class="demo-grid">
            <div class="main-content">
                <h2>主内容区域</h2>
                <p>这里是生成图片的主要显示区域</p>
                <p>生成的图片会在这里显示，并自动添加到右侧历史面板</p>
            </div>
            
            <div class="history-panel">
                <h4>历史图片</h4>
                <div class="history-images-container">
                    <div class="history-image-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzQzYTQwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2FhYSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzwvdGV4dD48L3N2Zz4=" alt="历史图片1">
                        <div class="history-image-info">种子: 12345</div>
                        <div class="history-image-time">14:30:25</div>
                    </div>
                    <div class="history-image-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzQzYTQwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2FhYSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzwvdGV4dD48L3N2Zz4=" alt="历史图片2">
                        <div class="history-image-info">种子: 67890</div>
                        <div class="history-image-time">14:25:10</div>
                    </div>
                    <div class="history-image-item">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzQzYTQwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2FhYSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzwvdGV4dD48L3N2Zz4=" alt="历史图片3">
                        <div class="history-image-info">种子: 11111</div>
                        <div class="history-image-time">14:20:05</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="feature-list">
            <h3>历史图片功能特性</h3>
            <ul>
                <li><strong>自动记录：</strong>每次生成的图片会自动添加到历史记录</li>
                <li><strong>限制数量：</strong>只显示最近生成的3张图片</li>
                <li><strong>详细信息：</strong>显示种子值、生成时间等参数</li>
                <li><strong>点击查看：</strong>点击历史图片可查看大图和完整参数</li>
                <li><strong>响应式设计：</strong>移动端自动隐藏历史面板</li>
                <li><strong>美观界面：</strong>现代化的UI设计和交互效果</li>
                <li><strong>悬停效果：</strong>鼠标悬停时的视觉反馈</li>
            </ul>
        </div>
        
        <div class="feature-list">
            <h3>改进后的模态框特性</h3>
            <ul>
                <li><strong>左右分栏布局：</strong>图片在左侧，参数信息在右侧</li>
                <li><strong>更大显示空间：</strong>使用uk-modal-large提供更好的查看体验</li>
                <li><strong>标签化参数：</strong>选择参数以标签形式显示，更加美观</li>
                <li><strong>卡片式设计：</strong>信息面板使用卡片样式，层次清晰</li>
                <li><strong>响应式适配：</strong>移动端自动调整为垂直布局</li>
                <li><strong>图片自适应：</strong>图片保持比例，自适应显示</li>
                <li><strong>解决排版问题：</strong>彻底解决了UI排版错乱的问题</li>
            </ul>
        </div>
        
        <div class="feature-list">
            <h3>响应式浏览器适配</h3>
            <ul>
                <li><strong>大屏幕适配 (≥1200px)：</strong>85vw × 90vh，充分利用空间</li>
                <li><strong>中等屏幕适配 (769px-1199px)：</strong>90vw × 92vh，平衡显示效果</li>
                <li><strong>小屏幕适配 (≤768px)：</strong>98vw × 98vh，最大化显示空间</li>
                <li><strong>超小屏幕适配 (≤480px)：</strong>100vw × 100vh，全屏显示</li>
                <li><strong>动态图片容器：</strong>根据屏幕尺寸调整图片显示高度</li>
                <li><strong>自适应字体：</strong>文字大小根据屏幕尺寸动态调整</li>
                <li><strong>优化间距：</strong>padding和margin根据屏幕尺寸优化</li>
                <li><strong>完美居中：</strong>模态框在各种屏幕尺寸下都能完美居中</li>
            </ul>
        </div>
    </div>
</body>
</html> 