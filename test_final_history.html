<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终历史记录测试</title>
    <link rel="stylesheet" type="text/css" href="web/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="web/css/dungeon.css">
    <style>
        body {
            background: #1e2328;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: #222A30;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .test-button {
            background: #13669e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #0f5a8a;
        }
        .refresh-button {
            background: #dc3545;
        }
        .refresh-button:hover {
            background: #c82333;
        }
        .test-layout {
            display: flex;
            gap: 20px;
        }
        .history-test-panel {
            background: #222A30;
            border: 1px solid #394650;
            border-radius: 8px;
            padding: 20px;
            width: 300px;
            height: 500px;
            overflow-y: auto;
        }
        .instructions {
            background: #171d21;
            border: 1px solid #394650;
            border-radius: 4px;
            padding: 15px;
            flex: 1;
        }
        .step {
            margin-bottom: 15px;
            padding: 10px;
            background: #2a3138;
            border-radius: 4px;
        }
        .step-number {
            background: #13669e;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>历史记录功能最终测试</h1>
        
        <div class="test-section">
            <h2>测试操作</h2>
            <div class="test-buttons">
                <button class="test-button" onclick="addTestImage()">添加测试图片</button>
                <button class="test-button" onclick="addMultipleImages()">添加5张图片</button>
                <button class="test-button" onclick="clearAllHistory()">清空历史</button>
                <button class="test-button" onclick="checkStorage()">检查存储</button>
                <button class="test-button refresh-button" onclick="location.reload()">🔄 刷新页面测试</button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-layout">
                <div class="history-test-panel">
                    <div class="history-header">
                        <h4 class="uk-text-center uk-margin-small">历史图片</h4>
                        <button id="clear-history" class="uk-button uk-button-small uk-button-secondary" type="button" title="清空历史记录">
                            <span uk-icon="icon: trash; ratio: 0.8"></span>
                        </button>
                    </div>
                    <div id="history-images" class="history-images-container">
                        <!-- 历史图片将在这里动态显示 -->
                    </div>
                </div>
                
                <div class="instructions">
                    <h3>测试步骤</h3>
                    
                    <div class="step">
                        <span class="step-number">1</span>
                        <strong>添加图片：</strong>点击"添加测试图片"或"添加5张图片"按钮，观察历史面板的变化
                    </div>
                    
                    <div class="step">
                        <span class="step-number">2</span>
                        <strong>检查数量：</strong>确认标题显示正确的数量格式："历史图片 (X/5)"
                    </div>
                    
                    <div class="step">
                        <span class="step-number">3</span>
                        <strong>验证存储：</strong>点击"检查存储"按钮，确认数据已保存到localStorage
                    </div>
                    
                    <div class="step">
                        <span class="step-number">4</span>
                        <strong>刷新测试：</strong>点击"🔄 刷新页面测试"按钮，页面刷新后历史图片应该仍然显示
                    </div>
                    
                    <div class="step">
                        <span class="step-number">5</span>
                        <strong>清理测试：</strong>点击垃圾桶图标或"清空历史"按钮，确认可以清空所有记录
                    </div>
                    
                    <div class="step">
                        <span class="step-number">6</span>
                        <strong>时间显示：</strong>观察时间显示格式，应该显示"刚刚"、"X分钟前"等友好格式
                    </div>
                    
                    <div style="margin-top: 20px; padding: 15px; background: #0d4f3c; border-radius: 4px;">
                        <strong>✅ 成功标准：</strong>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>刷新页面后历史图片正常显示</li>
                            <li>数量显示正确 (X/5)</li>
                            <li>时间格式友好</li>
                            <li>清理功能正常</li>
                            <li>最多保存5张图片</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="web/js/uikit.min.js"></script>
    <script src="web/js/uikit-icons.min.js"></script>
    <script>
        // 使用与主页面相同的逻辑
        let imageHistory = [];
        const MAX_HISTORY = 5;
        const STORAGE_KEY = 'dungeon_image_history';
        
        // 生成测试图片
        function generateTestImageUrl(index) {
            const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8', 'DDA0DD', 'F0E68C', 'FFB6C1'];
            const color = colors[index % colors.length];
            return `data:image/svg+xml;base64,${btoa(`
                <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#${color}"/>
                    <text x="50%" y="40%" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dy=".3em">
                        测试图片 ${index + 1}
                    </text>
                    <text x="50%" y="60%" font-family="Arial" font-size="12" fill="white" text-anchor="middle" dy=".3em">
                        ${new Date().toLocaleTimeString()}
                    </text>
                </svg>
            `)}`;
        }
        
        // 从localStorage加载历史记录 - 与主页面相同的逻辑
        function loadHistoryFromStorage() {
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                
                if (stored) {
                    const parsedHistory = JSON.parse(stored);
                    
                    imageHistory = parsedHistory.map(item => ({
                        ...item,
                        timestamp: new Date(item.timestamp)
                    })).slice(0, MAX_HISTORY);
                    
                    updateHistoryDisplay();
                } else {
                    updateHistoryDisplay();
                }
            } catch (error) {
                console.warn('加载历史记录失败:', error);
                imageHistory = [];
                updateHistoryDisplay();
            }
        }
        
        // 保存历史记录到localStorage
        function saveHistoryToStorage() {
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(imageHistory));
            } catch (error) {
                console.warn('保存历史记录失败:', error);
            }
        }
        
        // 更新历史图片显示 - 与主页面相同的逻辑
        function updateHistoryDisplay() {
            const historyImagesElement = document.getElementById('history-images');
            if (!historyImagesElement) {
                console.warn('找不到历史图片容器元素');
                return;
            }
            
            historyImagesElement.innerHTML = '';
            
            const historyTitle = document.querySelector('.history-header h4');
            if (historyTitle) {
                historyTitle.textContent = `历史图片 (${imageHistory.length}/${MAX_HISTORY})`;
            }
            
            if (imageHistory.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'history-empty-message';
                emptyMessage.innerHTML = `
                    <div style="text-align: center; color: #aaa; padding: 20px;">
                        <span uk-icon="icon: image; ratio: 2"></span>
                        <p style="margin-top: 10px;">暂无历史图片</p>
                        <p style="font-size: 0.8rem;">点击"添加测试图片"开始测试</p>
                    </div>
                `;
                historyImagesElement.appendChild(emptyMessage);
                return;
            }
            
            imageHistory.forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-image-item';
                
                const img = document.createElement('img');
                img.src = item.imageUrl;
                img.alt = '历史图片';
                img.title = '点击查看大图';
                
                const info = document.createElement('div');
                info.className = 'history-image-info';
                info.textContent = `种子: ${item.seed}`;
                
                const time = document.createElement('div');
                time.className = 'history-image-time';
                
                const now = new Date();
                const diffMs = now - item.timestamp;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                
                if (diffMins < 1) {
                    time.textContent = '刚刚';
                } else if (diffMins < 60) {
                    time.textContent = `${diffMins}分钟前`;
                } else if (diffHours < 24) {
                    time.textContent = `${diffHours}小时前`;
                } else {
                    time.textContent = item.timestamp.toLocaleDateString();
                }
                
                historyItem.appendChild(img);
                historyItem.appendChild(info);
                historyItem.appendChild(time);
                historyImagesElement.appendChild(historyItem);
            });
        }
        
        // 添加测试图片
        function addTestImage() {
            const historyItem = {
                id: Date.now(),
                imageUrl: generateTestImageUrl(imageHistory.length),
                timestamp: new Date(),
                seed: Math.floor(Math.random() * 999999),
                futureTech: ['测试科技A', '测试科技B'],
                futureCity: ['测试城市A', '测试城市B'],
                atmosphere: ['测试氛围']
            };
            
            imageHistory.unshift(historyItem);
            
            if (imageHistory.length > MAX_HISTORY) {
                imageHistory = imageHistory.slice(0, MAX_HISTORY);
            }
            
            updateHistoryDisplay();
            saveHistoryToStorage();
        }
        
        // 添加多张图片
        function addMultipleImages() {
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    addTestImage();
                }, i * 200);
            }
        }
        
        // 清空历史记录
        function clearAllHistory() {
            if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
                imageHistory = [];
                updateHistoryDisplay();
                localStorage.removeItem(STORAGE_KEY);
                alert('历史记录已清空');
            }
        }
        
        // 检查存储
        function checkStorage() {
            const stored = localStorage.getItem(STORAGE_KEY);
            const size = stored ? new Blob([stored]).size : 0;
            alert(`存储检查结果：\n历史记录数量: ${imageHistory.length}\n存储大小: ${size} 字节\n数据存在: ${stored ? '是' : '否'}`);
        }
        
        // 页面加载时初始化 - 与主页面相同的逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定清理按钮事件
            const clearBtn = document.getElementById('clear-history');
            if (clearBtn) {
                clearBtn.addEventListener('click', clearAllHistory);
            }
            
            // 延迟加载历史记录，确保DOM完全准备好
            setTimeout(() => {
                loadHistoryFromStorage();
            }, 200);
        });
        
        // 备用加载方式
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (imageHistory.length === 0) {
                    loadHistoryFromStorage();
                }
            }, 300);
        });
    </script>
</body>
</html>
