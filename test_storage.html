<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>localStorage测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>localStorage功能测试</h1>
    
    <div class="test-box">
        <h2>基础localStorage测试</h2>
        <button onclick="testBasicStorage()">测试基础存储</button>
        <button onclick="testDungeonStorage()">测试Dungeon存储</button>
        <button onclick="clearAllStorage()">清空所有存储</button>
        <button onclick="showAllStorage()">显示所有存储</button>
        <div id="basic-output" class="output"></div>
    </div>
    
    <div class="test-box">
        <h2>Dungeon历史记录测试</h2>
        <button onclick="addDungeonHistory()">添加历史记录</button>
        <button onclick="loadDungeonHistory()">加载历史记录</button>
        <button onclick="clearDungeonHistory()">清空Dungeon历史</button>
        <div id="dungeon-output" class="output"></div>
    </div>

    <script>
        const STORAGE_KEY = 'dungeon_image_history';
        
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }
        
        function testBasicStorage() {
            const output = document.getElementById('basic-output');
            output.textContent = '';
            
            try {
                // 测试基础存储
                localStorage.setItem('test_key', 'test_value');
                const value = localStorage.getItem('test_key');
                log('basic-output', `存储测试: ${value === 'test_value' ? '成功' : '失败'}`);
                
                // 测试JSON存储
                const testObj = { name: '测试', time: new Date().toISOString() };
                localStorage.setItem('test_json', JSON.stringify(testObj));
                const loadedObj = JSON.parse(localStorage.getItem('test_json'));
                log('basic-output', `JSON存储测试: ${loadedObj.name === '测试' ? '成功' : '失败'}`);
                
                // 显示存储大小
                const allKeys = Object.keys(localStorage);
                log('basic-output', `当前localStorage键数量: ${allKeys.length}`);
                log('basic-output', `所有键: ${allKeys.join(', ')}`);
                
            } catch (error) {
                log('basic-output', `存储测试失败: ${error.message}`);
            }
        }
        
        function testDungeonStorage() {
            const output = document.getElementById('basic-output');
            
            try {
                const testHistory = [
                    {
                        id: Date.now(),
                        imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDI4NWY0Ii8+PC9zdmc+',
                        timestamp: new Date().toISOString(),
                        seed: 12345,
                        futureTech: ['测试科技'],
                        futureCity: ['测试城市'],
                        atmosphere: ['测试氛围']
                    }
                ];
                
                localStorage.setItem(STORAGE_KEY, JSON.stringify(testHistory));
                log('basic-output', `Dungeon存储测试: 已保存测试数据`);
                
                const loaded = localStorage.getItem(STORAGE_KEY);
                const parsed = JSON.parse(loaded);
                log('basic-output', `Dungeon加载测试: ${parsed.length > 0 ? '成功' : '失败'}`);
                log('basic-output', `加载的数据: ${JSON.stringify(parsed[0], null, 2)}`);
                
            } catch (error) {
                log('basic-output', `Dungeon存储测试失败: ${error.message}`);
            }
        }
        
        function addDungeonHistory() {
            const output = document.getElementById('dungeon-output');
            output.textContent = '';
            
            try {
                // 获取现有历史记录
                let history = [];
                const existing = localStorage.getItem(STORAGE_KEY);
                if (existing) {
                    history = JSON.parse(existing);
                }
                
                // 添加新记录
                const newItem = {
                    id: Date.now(),
                    imageUrl: `data:image/svg+xml;base64,${btoa(`<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#${Math.floor(Math.random()*16777215).toString(16)}"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white">测试${history.length + 1}</text></svg>`)}`,
                    timestamp: new Date().toISOString(),
                    seed: Math.floor(Math.random() * 999999),
                    futureTech: ['测试科技' + (history.length + 1)],
                    futureCity: ['测试城市' + (history.length + 1)],
                    atmosphere: ['测试氛围' + (history.length + 1)]
                };
                
                history.unshift(newItem);
                if (history.length > 5) {
                    history = history.slice(0, 5);
                }
                
                localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
                log('dungeon-output', `添加历史记录成功，当前数量: ${history.length}`);
                
            } catch (error) {
                log('dungeon-output', `添加历史记录失败: ${error.message}`);
            }
        }
        
        function loadDungeonHistory() {
            const output = document.getElementById('dungeon-output');
            
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                if (stored) {
                    const history = JSON.parse(stored);
                    log('dungeon-output', `加载历史记录成功，数量: ${history.length}`);
                    history.forEach((item, index) => {
                        log('dungeon-output', `记录${index + 1}: 种子=${item.seed}, 时间=${item.timestamp}`);
                    });
                } else {
                    log('dungeon-output', '没有找到历史记录');
                }
            } catch (error) {
                log('dungeon-output', `加载历史记录失败: ${error.message}`);
            }
        }
        
        function clearDungeonHistory() {
            localStorage.removeItem(STORAGE_KEY);
            document.getElementById('dungeon-output').textContent = '';
            log('dungeon-output', 'Dungeon历史记录已清空');
        }
        
        function clearAllStorage() {
            localStorage.clear();
            document.getElementById('basic-output').textContent = '';
            document.getElementById('dungeon-output').textContent = '';
            log('basic-output', '所有localStorage数据已清空');
        }
        
        function showAllStorage() {
            const output = document.getElementById('basic-output');
            
            const allKeys = Object.keys(localStorage);
            log('basic-output', `localStorage中的所有键 (${allKeys.length}个):`);
            
            allKeys.forEach(key => {
                const value = localStorage.getItem(key);
                const size = new Blob([value]).size;
                log('basic-output', `${key}: ${size} 字节`);
                
                if (key === STORAGE_KEY) {
                    try {
                        const parsed = JSON.parse(value);
                        log('basic-output', `  -> Dungeon历史记录数量: ${parsed.length}`);
                    } catch (e) {
                        log('basic-output', `  -> 解析失败: ${e.message}`);
                    }
                }
            });
        }
        
        // 页面加载时显示当前状态
        window.addEventListener('load', function() {
            showAllStorage();
        });
    </script>
</body>
</html>
