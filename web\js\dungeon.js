(async (window, d, undefined) => {
    const _ = (selector, contex=d) => contex.querySelector(selector);

    let startTime;
    function timerStart() { startTime = new Date(); }
    function elapsedTime() { if (!startTime) return 0; return (new Date() - startTime) / 1000; }

    function seed () { return Math.floor(Math.random() * 9999999999); }

    function toggleDisplay(el, value=null) {
        if (value !== null) {
            el.style.display = (value === true) ? '' : 'none';
            return;
        }

        el.style.display = (el.style.display === 'none') ? '' : 'none';
    }

    // Seeded random number generator
    function seededRandom(a) {
        return function() {
          a |= 0; a = a + 0x9e3779b9 | 0;
          var t = a ^ a >>> 16; t = Math.imul(t, 0x21f0aaad);
              t = t ^ t >>> 15; t = Math.imul(t, 0x735a2d97);
          return ((t = t ^ t >>> 15) >>> 0) / 4294967296;
        }
    }

    // UUID generator
    function uuidv4() { return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c => (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)); }

    // Preload all API workflows
    async function load_api_workflows() {
        let wf = {
            'future_city': '/dungeon/js/future_city.json',
        }

        for (let key in wf) {
            let response = await fetch(wf[key]);
            wf[key] = await response.json();
        }

        return wf;
    }
    const workflows = await load_api_workflows();

    // Queue a prompt
    async function queue_prompt(prompt = {}) {
        const data = { 'prompt': prompt, 'client_id': client_id };

        const response = await fetch('/prompt', {
            method: 'POST',
            cache: 'no-cache',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        return await response.json();
    }

    // Interrupt the generation
    async function interrupt() {
        const response = await fetch('/interrupt', {
            method: 'POST',
            cache: 'no-cache',
            headers: {
                'Content-Type': 'text/html'
            },
        });

        //return await response.json();
    }

    const client_id = uuidv4();
    const server_address = window.location.hostname + ':' + window.location.port;

    // Current status
    let IS_GENERATING = false;

    // HTML elements
    const roll = _('#roll');
    const roll_icon = _('#roll-icon');
    const progressbar = _('#main-progress');
    const seed_input = _('#main-seed');
    const is_random_input = _('#is-random');
    const spinner = _('#main-spinner');
    const modal = _('#app-modal');
    const results = _('#results');
    const historyImages = _('#history-images');

    // 历史图片管理
    let imageHistory = [];
    const MAX_HISTORY = 3;

    // 添加图片到历史记录
    function addToHistory(imageData) {
        const historyItem = {
            id: Date.now(),
            imageUrl: imageData.src,
            timestamp: new Date(),
            seed: seed_input.value,
            futureTech: getSelectedFutureTech(),
            futureCity: getSelectedFutureCity(),
            atmosphere: getSelectedAtmosphere()
        };

        imageHistory.unshift(historyItem); // 添加到开头
        
        // 保持最多3张图片
        if (imageHistory.length > MAX_HISTORY) {
            imageHistory = imageHistory.slice(0, MAX_HISTORY);
        }

        updateHistoryDisplay();
    }

    // 更新历史图片显示
    function updateHistoryDisplay() {
        historyImages.innerHTML = '';
        
        imageHistory.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-image-item';
            
            const img = document.createElement('img');
            img.src = item.imageUrl;
            img.alt = '历史图片';
            img.title = '点击查看大图';
            
            // 点击历史图片可以放大查看
            img.addEventListener('click', () => {
                const modal = document.createElement('div');
                modal.className = 'uk-modal';
                modal.setAttribute('uk-modal', '');
                modal.innerHTML = `
                    <div class="uk-modal-dialog uk-modal-large">
                        <button class="uk-modal-close-default" type="button" uk-close></button>
                        <div class="uk-modal-header">
                            <h2 class="uk-modal-title">图片详情</h2>
                        </div>
                        <div class="uk-modal-body">
                            <div class="uk-grid-collapse uk-height-1-1" uk-grid>
                                <div class="uk-width-2-3@m uk-height-1-1">
                                    <div class="image-container">
                                        <img src="${item.imageUrl}" alt="历史图片">
                                    </div>
                                </div>
                                <div class="uk-width-1-3@m">
                                    <div class="info-panel">
                                        <div class="info-panel-content">
                                            <h3 class="uk-heading-small">生成参数</h3>
                                            <div class="parameter-item">
                                                <span class="parameter-label">种子值：</span>
                                                <span class="parameter-value">${item.seed}</span>
                                            </div>
                                            <div class="parameter-item">
                                                <span class="parameter-label">未来科技：</span>
                                                <div class="parameter-tags">
                                                    ${item.futureTech.map(tech => `<span class="tag">${tech}</span>`).join('')}
                                                </div>
                                            </div>
                                            <div class="parameter-item">
                                                <span class="parameter-label">未来城市：</span>
                                                <div class="parameter-tags">
                                                    ${item.futureCity.map(city => `<span class="tag">${city}</span>`).join('')}
                                                </div>
                                            </div>
                                            <div class="parameter-item">
                                                <span class="parameter-label">氛围光照：</span>
                                                <div class="parameter-tags">
                                                    ${item.atmosphere.map(light => `<span class="tag">${light}</span>`).join('')}
                                                </div>
                                            </div>
                                            <div class="parameter-item">
                                                <span class="parameter-label">生成时间：</span>
                                                <span class="parameter-value">${item.timestamp.toLocaleString()}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                UIkit.modal(modal).show();
                
                // 模态框关闭后移除元素
                modal.addEventListener('hidden', () => {
                    modal.remove();
                });
            });
            
            const info = document.createElement('div');
            info.className = 'history-image-info';
            info.textContent = `种子: ${item.seed}`;
            
            const time = document.createElement('div');
            time.className = 'history-image-time';
            time.textContent = item.timestamp.toLocaleTimeString();
            
            historyItem.appendChild(img);
            historyItem.appendChild(info);
            historyItem.appendChild(time);
            historyImages.appendChild(historyItem);
        });
    }

    // 未来科技选择复选框
    const future_tech_checkboxes = document.querySelectorAll('.future-tech-checkbox');
    // 未来城市选择复选框
    const future_city_checkboxes = document.querySelectorAll('.future-city-checkbox');
    // 氛围光照选择复选框
    const atmosphere_checkboxes = document.querySelectorAll('.atmosphere-checkbox');
    
    function updateProgress(max=0, value=0) { progressbar.max = max; progressbar.value = value; }

    // 获取选中的未来科技选项
    function getSelectedFutureTech() {
        const selected = [];
        future_tech_checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selected.push(checkbox.getAttribute('data-value'));
            }
        });
        return selected;
    }

    // 获取选中的未来城市选项
    function getSelectedFutureCity() {
        const selected = [];
        future_city_checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selected.push(checkbox.getAttribute('data-value'));
            }
        });
        return selected;
    }

    // 获取选中的氛围光照选项
    function getSelectedAtmosphere() {
        const selected = [];
        atmosphere_checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selected.push(checkbox.getAttribute('data-value'));
            }
        });
        return selected;
    }

    // 验证选择数量
    function validateSelections() {
        const futureTechCount = getSelectedFutureTech().length;
        const futureCityCount = getSelectedFutureCity().length;
        const atmosphereCount = getSelectedAtmosphere().length;

        if (futureTechCount === 0) {
            alert('请至少选择1个未来科技元素');
            return false;
        }
        if (futureCityCount === 0) {
            alert('请至少选择1个未来城市元素');
            return false;
        }
        if (atmosphereCount === 0) {
            alert('请至少选择1个氛围光照');
            return false;
        }
        return true;
    }

    // 构建未来科技节点参数
    function buildFutureTechNode() {
        const selected = getSelectedFutureTech();
        const node = {
            "碳中和工厂": false,
            "飞行出租车": false,
            "磁悬浮列车": false,
            "屋顶花园": false,
            "快递无人机": false,
            "互动喷泉": false,
            "智能社区": false,
            "摩天农场": false,
            "自动驾驶巴士": false,
            "生态住宅": false,
            "太阳能屋顶": false,
            "风力发电塔": false,
            "智能路灯": false,
            "回收机器人": false,
            "雨水花园": false,
            "城市森林": false,
            "悬浮监控球": false,
            "磁悬浮滑板": false,
            "全息政务厅": false,
            "医疗传送舱": false
        };

        selected.forEach(item => {
            if (node.hasOwnProperty(item)) {
                node[item] = true;
            }
        });

        return node;
    }

    // 构建未来城市节点参数
    function buildFutureCityNode() {
        const selected = getSelectedFutureCity();
        const node = {
            "零碳城市": false,
            "空中出行": false,
            "地面交通": false,
            "城市绿化": false,
            "无人机配送": false,
            "智慧广场": false,
            "智慧居民区": false,
            "垂直农场": false,
            "智能交通": false,
            "绿色建筑": false,
            "太阳能板": false,
            "风力发电": false,
            "智能照明": false,
            "垃圾分类": false,
            "雨水收集": false,
            "生态公园": false,
            "智能监控": false,
            "共享出行": false,
            "数字政务": false,
            "智慧医疗": false
        };

        selected.forEach(item => {
            if (node.hasOwnProperty(item)) {
                node[item] = true;
            }
        });

        return node;
    }

    // 构建氛围光照节点参数
    function buildAtmosphereNode() {
        const selected = getSelectedAtmosphere();
        const node = {
            "温暖阳光": false,
            "柔和月光": false,
            "霓虹灯光": false,
            "黄昏余晖": false,
            "晨光熹微": false,
            "神秘阴影": false,
            "梦幻光晕": false,
            "科技蓝光": false,
            "金色夕阳": false,
            "柔和阴影": false,
            "强烈对比": false,
            "柔和漫射": false,
            "深邃黑暗": false,
            "明亮通透": false,
            "朦胧雾气": false,
            "闪烁星光": false,
            "温暖烛光": false,
            "冷峻白光": false,
            "柔和暖光": false,
            "神秘紫光": false
        };

        selected.forEach(item => {
            if (node.hasOwnProperty(item)) {
                node[item] = true;
            }
        });

        return node;
    }

    // Event listeners
    roll.addEventListener('click', async (event) => {
        if (IS_GENERATING) {
            await interrupt();
        } else {      
            // 验证选择
            if (!validateSelections()) {
                return;
            }

            let wf = structuredClone(workflows['future_city']);
            
            // 设置随机种子
            let current_seed = seed();
            if (!is_random_input.checked) {
                current_seed = parseInt(seed_input.value) || 0;
            }
            seed_input.value = current_seed;

            // 更新工作流中的节点参数
            wf['593']['inputs'] = buildFutureTechNode();
            wf['595']['inputs'] = buildFutureCityNode();
            wf['598']['inputs'] = buildAtmosphereNode();
            
            // 更新种子
            wf['571']['inputs']['seed'] = current_seed;
            wf['578']['inputs']['seed'] = current_seed;

            // 开始生成
            IS_GENERATING = true;
            timerStart();
            toggleDisplay(spinner, true);
            toggleDisplay(roll_icon, false);
            roll.textContent = '停止生成';
            updateProgress(0, 0);

            try {
                const response = await queue_prompt(wf);
                
                if (response.error) {
                    throw new Error(response.error);
                }

                // 监听生成进度
                const pollInterval = setInterval(async () => {
                    try {
                        const historyResponse = await fetch(`/history/${response.prompt_id}`);
                        const history = await historyResponse.json();
                        
                        if (history[response.prompt_id]) {
                            const prompt = history[response.prompt_id];
                            
                            if (prompt.outputs) {
                                clearInterval(pollInterval);
                                IS_GENERATING = false;
                                toggleDisplay(spinner, false);
                                toggleDisplay(roll_icon, true);
                                roll.textContent = '生成未来城市';
                                updateProgress(100, 100);

                                // 显示结果
                                results.innerHTML = '';
                                for (let node_id in prompt.outputs) {
                                    const output = prompt.outputs[node_id];
                                    if (output.images) {
                                        output.images.forEach(image => {
                                            const img = document.createElement('img');
                                            img.src = `/view?filename=${image.filename}&subfolder=${image.subfolder}&type=${image.type}`;
                                            
                                            // 设置基础样式
                                            img.style.margin = '10px auto';
                                            img.style.display = 'block';
                                            
                                            // 等待图片加载完成后优化显示
                                            img.onload = function() {
                                                const aspectRatio = this.naturalWidth / this.naturalHeight;
                                                const isPortrait = aspectRatio < 1; // 竖版图片
                                                
                                                // 根据图片方向设置合适的最大尺寸
                                                if (isPortrait) {
                                                    // 竖版图片：优先考虑高度，限制宽度
                                                    this.style.maxHeight = '85vh';
                                                    this.style.maxWidth = '60vw';
                } else {
                                                    // 横版图片：优先考虑宽度，限制高度
                                                    this.style.maxWidth = '90vw';
                                                    this.style.maxHeight = '70vh';
                                                }
                                                
                                                // 保持宽高比
                                                this.style.width = 'auto';
                                                this.style.height = 'auto';
                                                this.style.objectFit = 'contain';
                                                
                                                // 添加到历史记录
                                                addToHistory(this);
                                            };
                                            
                                            results.appendChild(img);
                                        });
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Polling error:', error);
                    }
                }, 1000);

            } catch (error) {
                console.error('Generation error:', error);
                IS_GENERATING = false;
                toggleDisplay(spinner, false);
                toggleDisplay(roll_icon, true);
                roll.textContent = '生成未来城市';
                
                // 显示错误信息
                const modalMessage = _('#modal-message');
                modalMessage.textContent = error.message || '生成过程中发生错误';
                UIkit.modal(modal).show();
            }
        }
    });

    // 移除选择数量限制，允许自由选择
    // 未来科技和未来城市复选框可以多选
    // 氛围光照单选框也可以多选

    // 设置默认选择
    document.addEventListener('DOMContentLoaded', function() {
        // 默认选择3个未来科技元素
        const defaultFutureTech = ['碳中和工厂', '快递无人机', '智能路灯'];
        defaultFutureTech.forEach(value => {
            const checkbox = document.querySelector(`.future-tech-checkbox[data-value="${value}"]`);
            if (checkbox) checkbox.checked = true;
        });

        // 默认选择3个未来城市元素
        const defaultFutureCity = ['零碳城市', '空中出行', '垂直农场'];
        defaultFutureCity.forEach(value => {
            const checkbox = document.querySelector(`.future-city-checkbox[data-value="${value}"]`);
            if (checkbox) checkbox.checked = true;
        });

        // 默认选择温暖阳光
        const defaultAtmosphere = document.querySelector('.atmosphere-checkbox[data-value="温暖阳光"]');
        if (defaultAtmosphere) defaultAtmosphere.checked = true;
    });

})(window, document);

