<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单JSON历史记录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .success {
            background: #28a745;
        }
        .success:hover {
            background: #218838;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JSON历史记录系统测试</h1>
        
        <div>
            <h2>测试操作</h2>
            <button onclick="testConnection()">🔗 测试连接</button>
            <button onclick="addTestRecord()" class="success">➕ 添加记录</button>
            <button onclick="getHistory()">📥 获取历史</button>
            <button onclick="getStatus()">📊 获取状态</button>
            <button onclick="clearHistory()" class="danger">🗑️ 清空历史</button>
            <button onclick="location.reload()">🔄 刷新页面</button>
        </div>
        
        <div id="status-area"></div>
        
        <div>
            <h3>API响应日志</h3>
            <div id="output" class="output">等待操作...</div>
        </div>
        
        <div>
            <h3>历史记录显示</h3>
            <div id="history-display" style="border: 1px solid #ccc; padding: 10px; min-height: 100px;">
                暂无历史记录
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/dungeon/api/history';
        
        function log(message, data = null) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            let logMessage = `[${timestamp}] ${message}`;
            
            if (data) {
                logMessage += '\n' + JSON.stringify(data, null, 2);
            }
            
            output.textContent = logMessage + '\n\n' + output.textContent;
        }
        
        function showStatus(message, isError = false) {
            const statusArea = document.getElementById('status-area');
            statusArea.innerHTML = `<div class="status ${isError ? 'error' : 'success'}">${message}</div>`;
        }
        
        async function testConnection() {
            try {
                log('测试API连接...');
                const response = await fetch(API_BASE + '/status');
                
                if (response.ok) {
                    const result = await response.json();
                    showStatus('✅ API连接成功');
                    log('连接测试成功', result);
                } else {
                    showStatus(`❌ API连接失败: ${response.status}`, true);
                    log(`连接测试失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                showStatus(`❌ 连接错误: ${error.message}`, true);
                log('连接测试错误', { error: error.message });
            }
        }
        
        async function addTestRecord() {
            try {
                const testData = {
                    imageUrl: `data:image/svg+xml;base64,${btoa(`
                        <svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                            <rect width="100%" height="100%" fill="#${Math.floor(Math.random()*16777215).toString(16)}"/>
                            <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white">测试</text>
                        </svg>
                    `)}`,
                    seed: Math.floor(Math.random() * 999999).toString(),
                    futureTech: ['测试科技A', '测试科技B'],
                    futureCity: ['测试城市A'],
                    atmosphere: ['测试氛围'],
                    imagePath: `/test/image_${Date.now()}.png`
                };
                
                log('添加测试记录...', testData);
                
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('✅ 记录添加成功');
                    log('添加记录成功', result);
                    // 自动刷新历史记录
                    await getHistory();
                } else {
                    showStatus(`❌ 添加失败: ${result.error}`, true);
                    log('添加记录失败', result);
                }
            } catch (error) {
                showStatus(`❌ 添加错误: ${error.message}`, true);
                log('添加记录错误', { error: error.message });
            }
        }
        
        async function getHistory() {
            try {
                log('获取历史记录...');
                const response = await fetch(API_BASE);
                const result = await response.json();
                
                if (result.success) {
                    showStatus(`✅ 获取到 ${result.count} 条记录`);
                    log('获取历史记录成功', result);
                    displayHistory(result.data);
                } else {
                    showStatus(`❌ 获取失败: ${result.error}`, true);
                    log('获取历史记录失败', result);
                }
            } catch (error) {
                showStatus(`❌ 获取错误: ${error.message}`, true);
                log('获取历史记录错误', { error: error.message });
            }
        }
        
        async function getStatus() {
            try {
                log('获取系统状态...');
                const response = await fetch(API_BASE + '/status');
                const result = await response.json();
                
                if (result.success) {
                    const status = result.data;
                    showStatus(`✅ 系统状态: ${status.count}/${status.max_count} 记录, 文件大小: ${status.file_size} 字节`);
                    log('获取状态成功', result);
                } else {
                    showStatus(`❌ 状态获取失败: ${result.error}`, true);
                    log('获取状态失败', result);
                }
            } catch (error) {
                showStatus(`❌ 状态错误: ${error.message}`, true);
                log('获取状态错误', { error: error.message });
            }
        }
        
        async function clearHistory() {
            if (!confirm('确定要清空所有历史记录吗？')) {
                return;
            }
            
            try {
                log('清空历史记录...');
                const response = await fetch(API_BASE, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('✅ 历史记录已清空');
                    log('清空历史记录成功', result);
                    displayHistory([]);
                } else {
                    showStatus(`❌ 清空失败: ${result.error}`, true);
                    log('清空历史记录失败', result);
                }
            } catch (error) {
                showStatus(`❌ 清空错误: ${error.message}`, true);
                log('清空历史记录错误', { error: error.message });
            }
        }
        
        function displayHistory(history) {
            const display = document.getElementById('history-display');
            
            if (!history || history.length === 0) {
                display.innerHTML = '<p style="color: #999; text-align: center;">暂无历史记录</p>';
                return;
            }
            
            let html = `<h4>历史记录 (${history.length}/5)</h4>`;
            
            history.forEach((item, index) => {
                const timestamp = new Date(item.timestamp);
                const timeStr = timestamp.toLocaleString();
                
                html += `
                    <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 4px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <img src="${item.imageUrl}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;">
                            <div>
                                <strong>种子:</strong> ${item.seed}<br>
                                <strong>时间:</strong> ${timeStr}<br>
                                <strong>科技:</strong> ${item.futureTech ? item.futureTech.join(', ') : '无'}<br>
                                <strong>城市:</strong> ${item.futureCity ? item.futureCity.join(', ') : '无'}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            display.innerHTML = html;
        }
        
        // 页面加载时自动测试连接和获取历史记录
        document.addEventListener('DOMContentLoaded', async function() {
            await testConnection();
            await getHistory();
        });
    </script>
</body>
</html>
