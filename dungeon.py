import torch
import random

class FutureTechNode:
    """未来科技选择节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "碳中和工厂": ("BOOLEAN", {"default": False}),
                "飞行出租车": ("BOOLEAN", {"default": False}),
                "磁悬浮列车": ("BOOLEAN", {"default": False}),
                "屋顶花园": ("BOOLEAN", {"default": False}),
                "快递无人机": ("BOOLEAN", {"default": False}),
                "互动喷泉": ("BOOLEAN", {"default": False}),
                "智能社区": ("BOOLEAN", {"default": False}),
                "摩天农场": ("BOOLEAN", {"default": False}),
                "自动驾驶巴士": ("BOOLEAN", {"default": False}),
                "生态住宅": ("BOOLEAN", {"default": False}),
                "太阳能屋顶": ("BOOLEAN", {"default": False}),
                "风力发电塔": ("BOOLEAN", {"default": False}),
                "智能路灯": ("BOOLEAN", {"default": False}),
                "回收机器人": ("BOOLEAN", {"default": False}),
                "雨水花园": ("BOOLEAN", {"default": False}),
                "城市森林": ("BOOLEAN", {"default": False}),
                "悬浮监控球": ("BOOLEAN", {"default": False}),
                "磁悬浮滑板": ("BOOLEAN", {"default": False}),
                "全息政务厅": ("BOOLEAN", {"default": False}),
                "医疗传送舱": ("BOOLEAN", {"default": False}),
            }
        }
    
    RETURN_TYPES = ("STRING",)
    FUNCTION = "process"
    CATEGORY = "未来城市生成器"
    
    def process(self, **kwargs):
        selected_items = []
        for key, value in kwargs.items():
            if value:
                selected_items.append(key)
        
        # 移除数量限制，允许选择任意数量
        result = ", ".join(selected_items) if selected_items else "碳中和工厂, 快递无人机, 智能路灯"
        return (result,)


class FutureCityNode:
    """未来城市选择节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "零碳城市": ("BOOLEAN", {"default": False}),
                "空中出行": ("BOOLEAN", {"default": False}),
                "地面交通": ("BOOLEAN", {"default": False}),
                "城市绿化": ("BOOLEAN", {"default": False}),
                "无人机配送": ("BOOLEAN", {"default": False}),
                "智慧广场": ("BOOLEAN", {"default": False}),
                "智慧居民区": ("BOOLEAN", {"default": False}),
                "垂直农场": ("BOOLEAN", {"default": False}),
                "智能交通": ("BOOLEAN", {"default": False}),
                "绿色建筑": ("BOOLEAN", {"default": False}),
                "太阳能板": ("BOOLEAN", {"default": False}),
                "风力发电": ("BOOLEAN", {"default": False}),
                "智能照明": ("BOOLEAN", {"default": False}),
                "垃圾分类": ("BOOLEAN", {"default": False}),
                "雨水收集": ("BOOLEAN", {"default": False}),
                "生态公园": ("BOOLEAN", {"default": False}),
                "智能监控": ("BOOLEAN", {"default": False}),
                "共享出行": ("BOOLEAN", {"default": False}),
                "数字政务": ("BOOLEAN", {"default": False}),
                "智慧医疗": ("BOOLEAN", {"default": False}),
            }
        }
    
    RETURN_TYPES = ("STRING",)
    FUNCTION = "process"
    CATEGORY = "未来城市生成器"
    
    def process(self, **kwargs):
        selected_items = []
        for key, value in kwargs.items():
            if value:
                selected_items.append(key)
        
        # 移除数量限制，允许选择任意数量
        result = ", ".join(selected_items) if selected_items else "零碳城市, 空中出行, 垂直农场"
        return (result,)


class AtmosphereNode:
    """氛围光照选择节点"""
    
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "温暖阳光": ("BOOLEAN", {"default": False}),
                "柔和月光": ("BOOLEAN", {"default": False}),
                "霓虹灯光": ("BOOLEAN", {"default": False}),
                "黄昏余晖": ("BOOLEAN", {"default": False}),
                "晨光熹微": ("BOOLEAN", {"default": False}),
                "神秘阴影": ("BOOLEAN", {"default": False}),
                "梦幻光晕": ("BOOLEAN", {"default": False}),
                "科技蓝光": ("BOOLEAN", {"default": False}),
                "金色夕阳": ("BOOLEAN", {"default": False}),
                "柔和阴影": ("BOOLEAN", {"default": False}),
                "强烈对比": ("BOOLEAN", {"default": False}),
                "柔和漫射": ("BOOLEAN", {"default": False}),
                "深邃黑暗": ("BOOLEAN", {"default": False}),
                "明亮通透": ("BOOLEAN", {"default": False}),
                "朦胧雾气": ("BOOLEAN", {"default": False}),
                "闪烁星光": ("BOOLEAN", {"default": False}),
                "温暖烛光": ("BOOLEAN", {"default": False}),
                "冷峻白光": ("BOOLEAN", {"default": False}),
                "柔和暖光": ("BOOLEAN", {"default": False}),
                "神秘紫光": ("BOOLEAN", {"default": False}),
            }
        }
    
    RETURN_TYPES = ("STRING",)
    FUNCTION = "process"
    CATEGORY = "未来城市生成器"
    
    def process(self, **kwargs):
        selected_items = []
        for key, value in kwargs.items():
            if value:
                selected_items.append(key)
        
        # 移除数量限制，允许选择任意数量
        result = ", ".join(selected_items) if selected_items else "温暖阳光"
        return (result,)


# 注册节点
NODE_CLASS_MAPPINGS = {
    "FutureTechNode": FutureTechNode,
    "FutureCityNode": FutureCityNode,
    "AtmosphereNode": AtmosphereNode,
}

NODE_DISPLAY_NAME_MAPPINGS = {
    "FutureTechNode": "未来科技选择",
    "FutureCityNode": "未来城市选择", 
    "AtmosphereNode": "氛围光照选择",
}
