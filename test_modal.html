<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框测试</title>
    <link rel="stylesheet" href="web/css/uikit.min.css">
    <link rel="stylesheet" href="web/css/dungeon.css">
</head>
<body>
    <div class="uk-container uk-container-expand">
        <h1>历史图片模态框测试</h1>
        <button class="uk-button uk-button-primary" onclick="showModal()">点击测试模态框</button>
    </div>

    <script src="web/js/uikit.min.js"></script>
    <script src="web/js/uikit-icons.min.js"></script>
    <script>
        function showModal() {
            const modal = document.createElement('div');
            modal.className = 'uk-modal';
            modal.setAttribute('uk-modal', '');
            modal.innerHTML = `
                <div class="uk-modal-dialog uk-modal-large">
                    <button class="uk-modal-close-default" type="button" uk-close></button>
                    <div class="uk-modal-header">
                        <h2 class="uk-modal-title">图片详情</h2>
                    </div>
                    <div class="uk-modal-body">
                        <div class="uk-grid-collapse uk-height-1-1" uk-grid>
                            <div class="uk-width-2-3@m uk-height-1-1">
                                <div class="image-container">
                                    <img src="https://picsum.photos/800/1200" alt="测试图片">
                                </div>
                            </div>
                            <div class="uk-width-1-3@m">
                                <div class="info-panel">
                                    <div class="info-panel-content">
                                        <h3 class="uk-heading-small">生成参数</h3>
                                        <div class="parameter-item">
                                            <span class="parameter-label">种子值:</span>
                                            <span class="parameter-value">12345</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">未来科技:</span>
                                            <div class="parameter-tags">
                                                <span class="tag">全息投影</span>
                                                <span class="tag">量子计算</span>
                                            </div>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">未来城市:</span>
                                            <div class="parameter-tags">
                                                <span class="tag">空中花园</span>
                                                <span class="tag">智能建筑</span>
                                            </div>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">氛围:</span>
                                            <div class="parameter-tags">
                                                <span class="tag">科幻</span>
                                                <span class="tag">未来感</span>
                                            </div>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">生成时间:</span>
                                            <span class="parameter-value">${new Date().toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            UIkit.modal(modal).show();
            
            // 模态框关闭后移除元素
            modal.addEventListener('hidden', () => {
                modal.remove();
            });
        }
    </script>
</body>
</html>
