<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框测试</title>
    <link rel="stylesheet" href="web/css/uikit.min.css">
    <link rel="stylesheet" href="web/css/dungeon.css">
</head>
<body>
    <div class="uk-container uk-container-expand">
        <h1>历史图片模态框测试</h1>
        <div class="uk-margin">
            <button class="uk-button uk-button-primary uk-margin-right" onclick="showModal('https://picsum.photos/800/1200')">测试竖版图片</button>
            <button class="uk-button uk-button-secondary uk-margin-right" onclick="showModal('https://picsum.photos/1200/800')">测试横版图片</button>
            <button class="uk-button uk-button-default" onclick="showModal('https://picsum.photos/800/800')">测试方形图片</button>
        </div>
    </div>

    <script src="web/js/uikit.min.js"></script>
    <script src="web/js/uikit-icons.min.js"></script>
    <script>
        function showModal(imageUrl = 'https://picsum.photos/800/1200') {
            const modal = document.createElement('div');
            modal.className = 'uk-modal';
            modal.setAttribute('uk-modal', '');
            modal.innerHTML = `
                <div class="uk-modal-dialog uk-modal-large">
                    <button class="uk-modal-close-default" type="button" uk-close></button>
                    <div class="uk-modal-header">
                        <h2 class="uk-modal-title">图片详情</h2>
                    </div>
                    <div class="uk-modal-body">
                        <div class="uk-grid-collapse uk-height-1-1" uk-grid>
                            <div class="uk-width-2-3@m uk-height-1-1">
                                <div class="image-container">
                                    <img src="${imageUrl}" alt="测试图片">
                                </div>
                            </div>
                            <div class="uk-width-1-3@m">
                                <div class="info-panel">
                                    <div class="info-panel-content">
                                        <h3 class="uk-heading-small">生成参数</h3>
                                        <div class="parameter-item">
                                            <span class="parameter-label">种子值:</span>
                                            <span class="parameter-value">12345</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">未来科技:</span>
                                            <div class="parameter-tags">
                                                <span class="tag">全息投影</span>
                                                <span class="tag">量子计算</span>
                                            </div>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">未来城市:</span>
                                            <div class="parameter-tags">
                                                <span class="tag">空中花园</span>
                                                <span class="tag">智能建筑</span>
                                            </div>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">氛围:</span>
                                            <div class="parameter-tags">
                                                <span class="tag">科幻</span>
                                                <span class="tag">未来感</span>
                                            </div>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="parameter-label">生成时间:</span>
                                            <span class="parameter-value">${new Date().toLocaleString()}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            UIkit.modal(modal).show();

            // 图片加载完成后调整模态框宽度
            const modalImg = modal.querySelector('.image-container img');
            const modalDialog = modal.querySelector('.uk-modal-dialog');

            modalImg.onload = function() {
                const imgAspectRatio = this.naturalWidth / this.naturalHeight;
                const imageContainer = this.closest('.uk-width-2-3\\@m');
                const grid = modal.querySelector('.uk-grid');

                // 确保网格始终使用flex布局
                grid.style.display = 'flex';
                grid.style.flexDirection = 'row';

                // 根据图片比例调整模态框
                if (imgAspectRatio < 0.8) {
                    // 竖版图片：更紧凑的布局
                    modalDialog.style.maxWidth = '70vw';
                    modalDialog.style.minWidth = '500px';
                }
                else if (imgAspectRatio <= 1.2) {
                    // 方形图片：中等布局
                    modalDialog.style.maxWidth = '75vw';
                    modalDialog.style.minWidth = '600px';
                }
                else {
                    // 横版图片：较宽布局
                    modalDialog.style.maxWidth = '85vw';
                    modalDialog.style.minWidth = '700px';
                }
            };

            // 模态框关闭后移除元素
            modal.addEventListener('hidden', () => {
                modal.remove();
            });
        }
    </script>
</body>
</html>
