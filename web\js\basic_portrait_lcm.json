{"1": {"inputs": {"ckpt_name": ""}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "2": {"inputs": {"lora_name": "lcm-lora-sdxl.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["1", 0], "clip": ["1", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "3": {"inputs": {"sampling": "lcm", "zsnr": false, "model": ["2", 0]}, "class_type": "ModelSamplingDiscrete", "_meta": {"title": "ModelSamplingDiscrete"}}, "4": {"inputs": {"width": 1024, "height": 1024, "size_cond_factor": 4, "text": "", "clip": ["2", 1]}, "class_type": "CLIPTextEncodeSDXL+", "_meta": {"title": "🔧 SDXLCLIPTextEncode"}}, "5": {"inputs": {"width": 1024, "height": 1024, "size_cond_factor": 4, "text": "", "clip": ["2", 1]}, "class_type": "CLIPTextEncodeSDXL+", "_meta": {"title": "🔧 SDXLCLIPTextEncode"}}, "6": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "7": {"inputs": {"seed": 0, "steps": 8, "cfg": 2.5, "sampler_name": "lcm", "scheduler": "normal", "denoise": 1, "model": ["14", 0], "positive": ["4", 0], "negative": ["5", 0], "latent_image": ["6", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "8": {"inputs": {"samples": ["7", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "13": {"inputs": {"filename_prefix": "Dungeon", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "14": {"inputs": {"multiplier": 0.7, "model": ["3", 0]}, "class_type": "RescaleCFG", "_meta": {"title": "RescaleCFG"}}}