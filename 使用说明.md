# 未来城市生成器 - 使用说明

## 🚀 快速开始

### 1. 安装插件
将整个 `Comfy_Dungeon` 文件夹复制到 ComfyUI 的 `custom_nodes` 目录中。

### 2. 安装依赖模型
确保您已安装以下模型文件：

#### 必需模型：
- **FLUX.1 DiT模型**: `svdq-int4_r32-flux.1-dev.safetensors`
- **FLUX.1 LoRA**: `F.1未来都市_TWO.safetensors`
- **CLIP文本编码器**: `clip_l.safetensors`, `awq-int4-flux.1-t5xxl.safetensors`
- **VAE**: `ae.safetensors`

#### 必需扩展：
- ComfyUI Essentials
- Nunchaku FLUX.1
- Qwen AI (用于文本处理)

### 3. 启动ComfyUI
启动ComfyUI后，访问：`http://localhost:8188/dungeon`

## 🎮 使用方法

### 界面说明
左侧面板包含三个主要分类：

#### 🏙️ 未来科技选择
从20个未来科技元素中选择任意数量：
- 碳中和工厂、飞行出租车、磁悬浮列车
- 屋顶花园、快递无人机、互动喷泉
- 智能社区、摩天农场、自动驾驶巴士
- 生态住宅、太阳能屋顶、风力发电塔
- 智能路灯、回收机器人、雨水花园
- 城市森林、悬浮监控球、磁悬浮滑板
- 全息政务厅、医疗传送舱

#### 🌆 未来城市选择
从20个未来城市特征中选择任意数量：
- 零碳城市、空中出行、地面交通
- 城市绿化、无人机配送、智慧广场
- 智慧居民区、垂直农场、智能交通
- 绿色建筑、太阳能板、风力发电
- 智能照明、垃圾分类、雨水收集
- 生态公园、智能监控、共享出行
- 数字政务、智慧医疗

#### 🌟 氛围光照选择
从20种氛围光照中选择任意数量：
- 温暖阳光、柔和月光、霓虹灯光
- 黄昏余晖、晨光熹微、神秘阴影
- 梦幻光晕、科技蓝光、金色夕阳
- 柔和阴影、强烈对比、柔和漫射
- 深邃黑暗、明亮通透、朦胧雾气
- 闪烁星光、温暖烛光、冷峻白光
- 柔和暖光、神秘紫光

### 生成步骤
1. **选择元素**: 在每个分类中选择任意数量的选项
2. **设置种子**: 可选择随机种子或指定固定种子
3. **点击生成**: 点击"生成未来城市"按钮
4. **等待结果**: 系统会自动处理并生成图像
5. **查看结果**: 生成的图像会显示在右侧区域

## ⚙️ 高级功能

### 种子控制
- **随机种子**: 勾选"随机"复选框，每次生成都会使用不同的种子
- **固定种子**: 取消勾选"随机"，在种子输入框中输入特定数值

### 生成过程
- 系统会验证您的选择是否符合要求
- 自动调用Qwen AI优化提示词描述
- 使用FLUX.1模型生成高质量未来城市图像
- 实时显示生成进度

## 🔧 故障排除

### 常见问题

**Q: 页面无法访问**
A: 确保ComfyUI正在运行，并检查端口号是否正确

**Q: 生成失败**
A: 检查是否安装了所有必需的模型和扩展

**Q: 选择数量错误**
A: 确保每个分类至少选择1个选项

**Q: 图像质量不佳**
A: 尝试不同的种子值或调整选择组合

### 错误信息
- "请至少选择1个未来科技元素" - 需要至少选择1个未来科技选项
- "请至少选择1个未来城市元素" - 需要至少选择1个未来城市选项  
- "请至少选择1个氛围光照" - 需要至少选择1个氛围光照选项

## 📝 提示和建议

### 最佳实践
1. **多样化选择**: 尝试不同的元素组合来获得独特效果
2. **氛围搭配**: 根据选择的科技和城市特征选择合适的氛围光照
3. **种子实验**: 使用不同的种子值来获得更多变体
4. **组合探索**: 尝试各种不同的元素组合
5. **灵活选择**: 可以根据需要选择任意数量的元素，不受限制

### 创意建议
- **环保主题**: 选择碳中和工厂、零碳城市、温暖阳光
- **科技主题**: 选择悬浮监控球、智能监控、科技蓝光
- **自然主题**: 选择城市森林、生态公园、柔和月光
- **未来交通**: 选择飞行出租车、空中出行、霓虹灯光

## 🆘 技术支持

如果遇到问题，请：
1. 检查所有依赖是否正确安装
2. 查看ComfyUI控制台的错误信息
3. 确认模型文件路径是否正确
4. 重启ComfyUI并重试

## 📄 许可证

本项目采用MIT许可证。详见LICENSE文件。 