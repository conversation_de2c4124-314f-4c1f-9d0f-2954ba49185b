#!/usr/bin/env python3
"""
测试图像显示优化功能
"""

import os

def test_css_modifications():
    """测试CSS修改"""
    css_path = "web/css/dungeon.css"
    
    if not os.path.exists(css_path):
        print(f"❌ CSS文件不存在: {css_path}")
        return False
    
    try:
        with open(css_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否添加了图像优化样式
        required_styles = [
            'object-fit: contain',
            'transition: all 0.3s ease',
            'display: flex',
            'flex-direction: column',
            'align-items: center',
            '@media screen'
        ]
        
        for style in required_styles:
            if style not in content:
                print(f"❌ CSS中缺少样式: {style}")
                return False
        
        print("✅ CSS修改正确")
        return True
        
    except Exception as e:
        print(f"❌ CSS测试错误: {e}")
        return False

def test_javascript_image_logic():
    """测试JavaScript图像显示逻辑"""
    js_path = "web/js/dungeon.js"
    
    if not os.path.exists(js_path):
        print(f"❌ JavaScript文件不存在: {js_path}")
        return False
    
    try:
        with open(js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查图像显示逻辑
        required_logic = [
            'aspectRatio = this.naturalWidth / this.naturalHeight',
            'isPortrait = aspectRatio < 1',
            'maxHeight',
            'maxWidth',
            'objectFit: contain'
        ]
        
        for logic in required_logic:
            if logic not in content:
                print(f"❌ JavaScript中缺少逻辑: {logic}")
                return False
        
        print("✅ JavaScript图像逻辑正确")
        return True
        
    except Exception as e:
        print(f"❌ JavaScript测试错误: {e}")
        return False

def test_responsive_design():
    """测试响应式设计"""
    css_path = "web/css/dungeon.css"
    
    if not os.path.exists(css_path):
        print(f"❌ CSS文件不存在: {css_path}")
        return False
    
    try:
        with open(css_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查响应式断点
        responsive_checks = [
            '@media screen and (max-width: 768px)',
            '@media screen and (min-width: 769px) and (max-width: 1200px)',
            '@media screen and (min-width: 1201px)',
            'max-width: 95vw',
            'max-width: 80vw',
            'max-width: 70vw'
        ]
        
        for check in responsive_checks:
            if check not in content:
                print(f"❌ CSS中缺少响应式设计: {check}")
                return False
        
        print("✅ 响应式设计正确")
        return True
        
    except Exception as e:
        print(f"❌ 响应式设计测试错误: {e}")
        return False

def test_image_orientation_logic():
    """测试图像方向判断逻辑"""
    js_path = "web/js/dungeon.js"
    
    if not os.path.exists(js_path):
        print(f"❌ JavaScript文件不存在: {js_path}")
        return False
    
    try:
        with open(js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查竖版和横版的处理逻辑
        portrait_logic = [
            '竖版图片：优先考虑高度',
            'maxHeight: 85vh',
            'maxWidth: 60vw'
        ]
        
        landscape_logic = [
            '横版图片：优先考虑宽度',
            'maxWidth: 90vw',
            'maxHeight: 70vh'
        ]
        
        for logic in portrait_logic:
            if logic not in content:
                print(f"❌ JavaScript中缺少竖版处理逻辑: {logic}")
                return False
        
        for logic in landscape_logic:
            if logic not in content:
                print(f"❌ JavaScript中缺少横版处理逻辑: {logic}")
                return False
        
        print("✅ 图像方向判断逻辑正确")
        return True
        
    except Exception as e:
        print(f"❌ 图像方向测试错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试图像显示优化...")
    print("=" * 60)
    
    tests = [
        ("CSS样式优化", test_css_modifications),
        ("JavaScript图像逻辑", test_javascript_image_logic),
        ("响应式设计", test_responsive_design),
        ("图像方向判断", test_image_orientation_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有图像显示优化测试通过！")
        print("\n📝 优化总结:")
        print("✅ 添加了智能图像方向判断")
        print("✅ 竖版图片优先考虑高度，限制宽度")
        print("✅ 横版图片优先考虑宽度，限制高度")
        print("✅ 保持图片原始宽高比")
        print("✅ 添加了响应式设计")
        print("✅ 优化了图像显示样式")
        print("✅ 图片居中显示")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
    
    return passed == total

if __name__ == "__main__":
    main() 