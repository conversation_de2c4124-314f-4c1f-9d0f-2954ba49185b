<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录功能测试</title>
    <link rel="stylesheet" type="text/css" href="web/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="web/css/dungeon.css">
    <style>
        body {
            background: #1e2328;
            color: white;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #222A30;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #13669e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #0f5a8a;
        }
        .history-test-panel {
            background: #222A30;
            border: 1px solid #394650;
            border-radius: 8px;
            padding: 20px;
            width: 300px;
            height: 500px;
            overflow-y: auto;
        }
        .info-box {
            background: #171d21;
            border: 1px solid #394650;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>历史记录功能测试</h1>
        
        <div class="test-section">
            <h2>功能说明</h2>
            <div class="info-box">
                <h3>新功能特性：</h3>
                <ul>
                    <li><strong>保存数量增加：</strong>从3张增加到5张历史图片</li>
                    <li><strong>持久化存储：</strong>使用localStorage保存，刷新页面后数据不丢失</li>
                    <li><strong>智能时间显示：</strong>显示"刚刚"、"X分钟前"、"X小时前"等友好格式</li>
                    <li><strong>清理功能：</strong>可以手动清空所有历史记录</li>
                    <li><strong>数量显示：</strong>标题显示当前数量 (X/5)</li>
                    <li><strong>空状态提示：</strong>无历史记录时显示友好提示</li>
                    <li><strong>存储管理：</strong>自动处理存储空间不足的情况</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>测试操作</h2>
            <div class="test-buttons">
                <button class="test-button" onclick="addTestImage()">添加测试图片</button>
                <button class="test-button" onclick="clearTestHistory()">清空历史记录</button>
                <button class="test-button" onclick="showStorageInfo()">查看存储信息</button>
                <button class="test-button" onclick="location.reload()">刷新页面测试持久化</button>
            </div>
            
            <div class="uk-grid uk-grid-small" uk-grid>
                <div class="uk-width-2-3">
                    <div class="info-box">
                        <h3>测试步骤：</h3>
                        <ol>
                            <li>点击"添加测试图片"按钮多次，观察历史记录变化</li>
                            <li>观察标题中的数量显示 (X/5)</li>
                            <li>添加超过5张图片，观察是否只保留最新的5张</li>
                            <li>点击"刷新页面测试持久化"，验证数据是否保存</li>
                            <li>点击历史图片查看大图和参数信息</li>
                            <li>点击清理按钮测试清空功能</li>
                        </ol>
                    </div>
                </div>
                <div class="uk-width-1-3">
                    <div class="history-test-panel">
                        <div class="history-header">
                            <h4 class="uk-text-center uk-margin-small">历史图片</h4>
                            <button id="clear-history" class="uk-button uk-button-small uk-button-secondary" type="button" title="清空历史记录">
                                <span uk-icon="icon: trash; ratio: 0.8"></span>
                            </button>
                        </div>
                        <div id="history-images" class="history-images-container">
                            <!-- 历史图片将在这里动态显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="web/js/uikit.min.js"></script>
    <script src="web/js/uikit-icons.min.js"></script>
    <script>
        // 模拟历史记录功能
        let imageHistory = [];
        const MAX_HISTORY = 5;
        const STORAGE_KEY = 'dungeon_image_history';
        
        // 生成测试图片URL
        function generateTestImageUrl(index) {
            const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8'];
            const color = colors[index % colors.length];
            return `data:image/svg+xml;base64,${btoa(`
                <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#${color}"/>
                    <text x="50%" y="50%" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dy=".3em">
                        测试图片 ${index + 1}
                    </text>
                    <text x="50%" y="70%" font-family="Arial" font-size="12" fill="white" text-anchor="middle" dy=".3em">
                        ${new Date().toLocaleTimeString()}
                    </text>
                </svg>
            `)}`;
        }
        
        // 从localStorage加载历史记录
        function loadHistoryFromStorage() {
            try {
                const stored = localStorage.getItem(STORAGE_KEY);
                if (stored) {
                    const parsedHistory = JSON.parse(stored);
                    imageHistory = parsedHistory.map(item => ({
                        ...item,
                        timestamp: new Date(item.timestamp)
                    })).slice(0, MAX_HISTORY);
                    updateHistoryDisplay();
                }
            } catch (error) {
                console.warn('加载历史记录失败:', error);
                imageHistory = [];
            }
        }
        
        // 保存历史记录到localStorage
        function saveHistoryToStorage() {
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(imageHistory));
            } catch (error) {
                console.warn('保存历史记录失败:', error);
            }
        }
        
        // 添加测试图片
        function addTestImage() {
            const historyItem = {
                id: Date.now(),
                imageUrl: generateTestImageUrl(imageHistory.length),
                timestamp: new Date(),
                seed: Math.floor(Math.random() * 999999),
                futureTech: ['测试科技A', '测试科技B'],
                futureCity: ['测试城市A', '测试城市B'],
                atmosphere: ['测试氛围']
            };
            
            imageHistory.unshift(historyItem);
            
            if (imageHistory.length > MAX_HISTORY) {
                imageHistory = imageHistory.slice(0, MAX_HISTORY);
            }
            
            updateHistoryDisplay();
            saveHistoryToStorage();
        }
        
        // 更新历史图片显示
        function updateHistoryDisplay() {
            const historyImages = document.getElementById('history-images');
            historyImages.innerHTML = '';
            
            // 更新标题显示当前数量
            const historyTitle = document.querySelector('.history-test-panel h4');
            if (historyTitle) {
                historyTitle.textContent = `历史图片 (${imageHistory.length}/${MAX_HISTORY})`;
            }
            
            if (imageHistory.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'history-empty-message';
                emptyMessage.innerHTML = `
                    <div style="text-align: center; color: #aaa; padding: 20px;">
                        <span uk-icon="icon: image; ratio: 2"></span>
                        <p style="margin-top: 10px;">暂无历史图片</p>
                        <p style="font-size: 0.8rem;">点击"添加测试图片"开始测试</p>
                    </div>
                `;
                historyImages.appendChild(emptyMessage);
                return;
            }
            
            imageHistory.forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-image-item';
                
                const img = document.createElement('img');
                img.src = item.imageUrl;
                img.alt = '历史图片';
                img.title = '点击查看大图';
                
                const info = document.createElement('div');
                info.className = 'history-image-info';
                info.textContent = `种子: ${item.seed}`;
                
                const time = document.createElement('div');
                time.className = 'history-image-time';
                
                // 显示友好的时间格式
                const now = new Date();
                const diffMs = now - item.timestamp;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                
                if (diffMins < 1) {
                    time.textContent = '刚刚';
                } else if (diffMins < 60) {
                    time.textContent = `${diffMins}分钟前`;
                } else if (diffHours < 24) {
                    time.textContent = `${diffHours}小时前`;
                } else {
                    time.textContent = item.timestamp.toLocaleDateString();
                }
                
                historyItem.appendChild(img);
                historyItem.appendChild(info);
                historyItem.appendChild(time);
                historyImages.appendChild(historyItem);
            });
        }
        
        // 清空历史记录
        function clearTestHistory() {
            if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
                imageHistory = [];
                updateHistoryDisplay();
                localStorage.removeItem(STORAGE_KEY);
            }
        }
        
        // 显示存储信息
        function showStorageInfo() {
            const stored = localStorage.getItem(STORAGE_KEY);
            const size = stored ? new Blob([stored]).size : 0;
            alert(`当前存储信息：\n历史记录数量: ${imageHistory.length}\n存储大小: ${size} 字节\n存储键: ${STORAGE_KEY}`);
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadHistoryFromStorage();
            
            // 绑定清理按钮事件
            const clearBtn = document.getElementById('clear-history');
            if (clearBtn) {
                clearBtn.addEventListener('click', clearTestHistory);
            }
        });
    </script>
</body>
</html>
