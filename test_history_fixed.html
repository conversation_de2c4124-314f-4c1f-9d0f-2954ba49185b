<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史记录修复测试</title>
    <link rel="stylesheet" type="text/css" href="web/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="web/css/dungeon.css">
    <style>
        body {
            background: #1e2328;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #222A30;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #13669e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0f5a8a;
        }
        .history-test-area {
            display: flex;
            gap: 20px;
        }
        .history-panel {
            background: #222A30;
            border: 1px solid #394650;
            border-radius: 8px;
            padding: 20px;
            width: 300px;
            height: 400px;
            overflow-y: auto;
        }
        .debug-info {
            background: #171d21;
            border: 1px solid #394650;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>历史记录功能修复测试</h1>
        
        <div class="test-section">
            <h2>测试操作</h2>
            <div class="test-buttons">
                <button class="test-button" onclick="addTestImage()">添加测试图片</button>
                <button class="test-button" onclick="manualLoad()">手动加载历史</button>
                <button class="test-button" onclick="clearHistory()">清空历史</button>
                <button class="test-button" onclick="showDebugInfo()">显示调试信息</button>
                <button class="test-button" onclick="location.reload()">刷新页面</button>
            </div>
        </div>

        <div class="test-section">
            <div class="history-test-area">
                <div class="history-panel">
                    <div class="history-header">
                        <h4 class="uk-text-center uk-margin-small">历史图片</h4>
                        <button id="clear-history" class="uk-button uk-button-small uk-button-secondary" type="button" title="清空历史记录">
                            <span uk-icon="icon: trash; ratio: 0.8"></span>
                        </button>
                    </div>
                    <div id="history-images" class="history-images-container">
                        <!-- 历史图片将在这里动态显示 -->
                    </div>
                </div>
                
                <div style="flex: 1;">
                    <h3>调试信息</h3>
                    <div id="debug-output" class="debug-info">页面加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="web/js/uikit.min.js"></script>
    <script src="web/js/uikit-icons.min.js"></script>
    <script>
        // 历史记录管理
        let imageHistory = [];
        const MAX_HISTORY = 5;
        const STORAGE_KEY = 'dungeon_image_history';
        
        function debugLog(message) {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.textContent += `[${timestamp}] ${message}\n`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
            console.log(message);
        }
        
        // 生成测试图片
        function generateTestImageUrl(index) {
            const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8'];
            const color = colors[index % colors.length];
            return `data:image/svg+xml;base64,${btoa(`
                <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#${color}"/>
                    <text x="50%" y="40%" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dy=".3em">
                        测试图片 ${index + 1}
                    </text>
                    <text x="50%" y="60%" font-family="Arial" font-size="12" fill="white" text-anchor="middle" dy=".3em">
                        ${new Date().toLocaleTimeString()}
                    </text>
                </svg>
            `)}`;
        }
        
        // 从localStorage加载历史记录
        function loadHistoryFromStorage() {
            try {
                debugLog('开始从localStorage加载历史记录...');
                const stored = localStorage.getItem(STORAGE_KEY);
                debugLog(`localStorage数据: ${stored ? '找到数据' : '无数据'}`);
                
                if (stored) {
                    const parsedHistory = JSON.parse(stored);
                    debugLog(`解析的历史记录数量: ${parsedHistory.length}`);
                    
                    imageHistory = parsedHistory.map(item => ({
                        ...item,
                        timestamp: new Date(item.timestamp)
                    })).slice(0, MAX_HISTORY);
                    
                    debugLog(`处理后的历史记录数量: ${imageHistory.length}`);
                    updateHistoryDisplay();
                } else {
                    debugLog('localStorage中没有历史记录');
                    updateHistoryDisplay();
                }
            } catch (error) {
                debugLog(`加载历史记录失败: ${error.message}`);
                imageHistory = [];
                updateHistoryDisplay();
            }
        }
        
        // 保存历史记录到localStorage
        function saveHistoryToStorage() {
            try {
                localStorage.setItem(STORAGE_KEY, JSON.stringify(imageHistory));
                debugLog(`保存历史记录成功，数量: ${imageHistory.length}`);
            } catch (error) {
                debugLog(`保存历史记录失败: ${error.message}`);
            }
        }
        
        // 更新历史图片显示
        function updateHistoryDisplay() {
            const historyImages = document.getElementById('history-images');
            debugLog(`更新历史图片显示，当前记录数量: ${imageHistory.length}`);
            debugLog(`historyImages元素: ${historyImages ? '找到' : '未找到'}`);
            
            if (!historyImages) {
                debugLog('错误: 找不到历史图片容器元素');
                return;
            }
            
            historyImages.innerHTML = '';
            
            // 更新标题显示当前数量
            const historyTitle = document.querySelector('.history-header h4');
            if (historyTitle) {
                historyTitle.textContent = `历史图片 (${imageHistory.length}/${MAX_HISTORY})`;
                debugLog(`更新标题: ${historyTitle.textContent}`);
            } else {
                debugLog('警告: 找不到历史面板标题元素');
            }
            
            if (imageHistory.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'history-empty-message';
                emptyMessage.innerHTML = `
                    <div style="text-align: center; color: #aaa; padding: 20px;">
                        <span uk-icon="icon: image; ratio: 2"></span>
                        <p style="margin-top: 10px;">暂无历史图片</p>
                        <p style="font-size: 0.8rem;">点击"添加测试图片"开始测试</p>
                    </div>
                `;
                historyImages.appendChild(emptyMessage);
                debugLog('显示空状态提示');
                return;
            }
            
            imageHistory.forEach((item, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-image-item';
                
                const img = document.createElement('img');
                img.src = item.imageUrl;
                img.alt = '历史图片';
                img.title = '点击查看大图';
                
                const info = document.createElement('div');
                info.className = 'history-image-info';
                info.textContent = `种子: ${item.seed}`;
                
                const time = document.createElement('div');
                time.className = 'history-image-time';
                
                const now = new Date();
                const diffMs = now - item.timestamp;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                
                if (diffMins < 1) {
                    time.textContent = '刚刚';
                } else if (diffMins < 60) {
                    time.textContent = `${diffMins}分钟前`;
                } else if (diffHours < 24) {
                    time.textContent = `${diffHours}小时前`;
                } else {
                    time.textContent = item.timestamp.toLocaleDateString();
                }
                
                historyItem.appendChild(img);
                historyItem.appendChild(info);
                historyItem.appendChild(time);
                historyImages.appendChild(historyItem);
                
                debugLog(`添加历史项目 ${index + 1}: 种子=${item.seed}`);
            });
        }
        
        // 添加测试图片
        function addTestImage() {
            const historyItem = {
                id: Date.now(),
                imageUrl: generateTestImageUrl(imageHistory.length),
                timestamp: new Date(),
                seed: Math.floor(Math.random() * 999999),
                futureTech: ['测试科技A', '测试科技B'],
                futureCity: ['测试城市A', '测试城市B'],
                atmosphere: ['测试氛围']
            };
            
            imageHistory.unshift(historyItem);
            
            if (imageHistory.length > MAX_HISTORY) {
                imageHistory = imageHistory.slice(0, MAX_HISTORY);
            }
            
            updateHistoryDisplay();
            saveHistoryToStorage();
            debugLog(`添加测试图片，当前总数: ${imageHistory.length}`);
        }
        
        // 手动加载历史
        function manualLoad() {
            debugLog('手动触发加载历史记录...');
            loadHistoryFromStorage();
        }
        
        // 清空历史记录
        function clearHistory() {
            if (confirm('确定要清空所有历史记录吗？')) {
                imageHistory = [];
                updateHistoryDisplay();
                localStorage.removeItem(STORAGE_KEY);
                debugLog('历史记录已清空');
            }
        }
        
        // 显示调试信息
        function showDebugInfo() {
            debugLog('=== 调试信息 ===');
            debugLog(`当前历史记录数量: ${imageHistory.length}`);
            debugLog(`localStorage键: ${STORAGE_KEY}`);
            debugLog(`localStorage数据存在: ${localStorage.getItem(STORAGE_KEY) ? '是' : '否'}`);
            debugLog(`historyImages元素: ${document.getElementById('history-images') ? '存在' : '不存在'}`);
            debugLog(`标题元素: ${document.querySelector('.history-header h4') ? '存在' : '不存在'}`);
            
            const allKeys = Object.keys(localStorage);
            debugLog(`localStorage所有键: ${allKeys.join(', ')}`);
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOMContentLoaded事件触发');
            setTimeout(() => {
                debugLog('延迟加载历史记录...');
                loadHistoryFromStorage();
            }, 200);
            
            // 绑定清理按钮
            const clearBtn = document.getElementById('clear-history');
            if (clearBtn) {
                clearBtn.addEventListener('click', clearHistory);
                debugLog('清理按钮事件已绑定');
            } else {
                debugLog('警告: 找不到清理按钮');
            }
        });
        
        // 备用加载
        window.addEventListener('load', function() {
            debugLog('window.load事件触发');
            setTimeout(() => {
                if (imageHistory.length === 0) {
                    debugLog('备用加载: 尝试加载历史记录...');
                    loadHistoryFromStorage();
                }
            }, 300);
        });
        
        debugLog('脚本初始化完成');
    </script>
</body>
</html>
