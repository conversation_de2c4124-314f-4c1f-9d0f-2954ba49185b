#!/usr/bin/env python3
"""
历史记录API - 处理历史图片记录的JSON文件存储
"""

import json
import os
import time
from datetime import datetime
from typing import List, Dict, Any
import base64
import uuid

class HistoryManager:
    def __init__(self, history_file: str = "history_records.json"):
        self.history_file = history_file
        self.max_history = 5
        
    def load_history(self) -> List[Dict[str, Any]]:
        """从JSON文件加载历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 确保数据格式正确
                    if isinstance(data, list):
                        return data[:self.max_history]  # 只返回最新的5条
                    else:
                        return []
            else:
                return []
        except Exception as e:
            print(f"加载历史记录失败: {e}")
            return []
    
    def save_history(self, history: List[Dict[str, Any]]) -> bool:
        """保存历史记录到JSON文件"""
        try:
            # 确保只保存最新的5条记录
            history = history[:self.max_history]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存历史记录失败: {e}")
            return False
    
    def add_record(self, image_data: Dict[str, Any]) -> bool:
        """添加新的历史记录"""
        try:
            # 加载现有历史记录
            history = self.load_history()
            
            # 创建新记录
            new_record = {
                "id": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
                "seed": image_data.get("seed", ""),
                "futureTech": image_data.get("futureTech", []),
                "futureCity": image_data.get("futureCity", []),
                "atmosphere": image_data.get("atmosphere", []),
                "imageUrl": image_data.get("imageUrl", ""),
                "imagePath": image_data.get("imagePath", "")  # 图片文件路径
            }
            
            # 添加到历史记录开头
            history.insert(0, new_record)
            
            # 保存更新后的历史记录
            return self.save_history(history)
            
        except Exception as e:
            print(f"添加历史记录失败: {e}")
            return False
    
    def clear_history(self) -> bool:
        """清空历史记录"""
        try:
            if os.path.exists(self.history_file):
                os.remove(self.history_file)
            return True
        except Exception as e:
            print(f"清空历史记录失败: {e}")
            return False
    
    def get_history_info(self) -> Dict[str, Any]:
        """获取历史记录信息"""
        history = self.load_history()
        file_size = 0
        if os.path.exists(self.history_file):
            file_size = os.path.getsize(self.history_file)
        
        return {
            "count": len(history),
            "max_count": self.max_history,
            "file_size": file_size,
            "file_exists": os.path.exists(self.history_file),
            "last_modified": os.path.getmtime(self.history_file) if os.path.exists(self.history_file) else None
        }

# 用于ComfyUI集成的函数
def get_history_manager():
    """获取历史记录管理器实例"""
    return HistoryManager()

def load_image_history():
    """加载图片历史记录"""
    manager = get_history_manager()
    return manager.load_history()

def save_image_to_history(image_data):
    """保存图片到历史记录"""
    manager = get_history_manager()
    return manager.add_record(image_data)

def clear_image_history():
    """清空图片历史记录"""
    manager = get_history_manager()
    return manager.clear_history()

def get_history_status():
    """获取历史记录状态"""
    manager = get_history_manager()
    return manager.get_history_info()

# 测试函数
if __name__ == "__main__":
    # 测试历史记录管理器
    manager = HistoryManager("test_history.json")
    
    # 添加测试记录
    test_data = {
        "seed": "123456",
        "futureTech": ["碳中和工厂", "快递无人机"],
        "futureCity": ["零碳城市", "空中出行"],
        "atmosphere": ["温暖阳光"],
        "imageUrl": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDI4NWY0Ii8+PC9zdmc+",
        "imagePath": "/path/to/image.png"
    }
    
    print("添加测试记录...")
    success = manager.add_record(test_data)
    print(f"添加结果: {success}")
    
    print("\n加载历史记录...")
    history = manager.load_history()
    print(f"历史记录数量: {len(history)}")
    for i, record in enumerate(history):
        print(f"记录 {i+1}: ID={record['id'][:8]}..., 种子={record['seed']}")
    
    print("\n历史记录信息...")
    info = manager.get_history_info()
    print(f"信息: {info}")
    
    # 清理测试文件
    if os.path.exists("test_history.json"):
        os.remove("test_history.json")
        print("\n测试文件已清理")
