<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON历史记录测试</title>
    <link rel="stylesheet" type="text/css" href="web/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="web/css/dungeon.css">
    <style>
        body {
            background: #1e2328;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: #222A30;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .test-button {
            background: #13669e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #0f5a8a;
        }
        .api-button {
            background: #28a745;
        }
        .api-button:hover {
            background: #218838;
        }
        .danger-button {
            background: #dc3545;
        }
        .danger-button:hover {
            background: #c82333;
        }
        .test-layout {
            display: flex;
            gap: 20px;
        }
        .history-panel {
            background: #222A30;
            border: 1px solid #394650;
            border-radius: 8px;
            padding: 20px;
            width: 350px;
            height: 500px;
            overflow-y: auto;
        }
        .api-info {
            background: #171d21;
            border: 1px solid #394650;
            border-radius: 4px;
            padding: 15px;
            flex: 1;
        }
        .api-response {
            background: #2a3138;
            border: 1px solid #394650;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-info {
            background: #0d4f3c;
            border: 1px solid #28a745;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
        .error-info {
            background: #4f0d0d;
            border: 1px solid #dc3545;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>JSON文件历史记录系统测试</h1>
        
        <div class="test-section">
            <h2>API测试操作</h2>
            <div class="test-buttons">
                <button class="test-button api-button" onclick="testGetHistory()">📥 获取历史记录</button>
                <button class="test-button api-button" onclick="testAddHistory()">➕ 添加历史记录</button>
                <button class="test-button api-button" onclick="testGetStatus()">📊 获取状态信息</button>
                <button class="test-button danger-button" onclick="testClearHistory()">🗑️ 清空历史记录</button>
                <button class="test-button" onclick="addMultipleRecords()">📚 添加多条记录</button>
                <button class="test-button danger-button" onclick="location.reload()">🔄 刷新页面测试</button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-layout">
                <div class="history-panel">
                    <div class="history-header">
                        <h4 class="uk-text-center uk-margin-small">历史图片</h4>
                        <button id="clear-history" class="uk-button uk-button-small uk-button-secondary" type="button" title="清空历史记录">
                            <span uk-icon="icon: trash; ratio: 0.8"></span>
                        </button>
                    </div>
                    <div id="history-images" class="history-images-container">
                        <!-- 历史图片将在这里动态显示 -->
                    </div>
                </div>
                
                <div class="api-info">
                    <h3>API测试信息</h3>
                    
                    <div id="status-display" class="status-info" style="display: none;">
                        <strong>✅ 系统状态正常</strong>
                        <div id="status-details"></div>
                    </div>
                    
                    <div id="error-display" class="error-info" style="display: none;">
                        <strong>❌ 系统错误</strong>
                        <div id="error-details"></div>
                    </div>
                    
                    <h4>API端点说明：</h4>
                    <ul>
                        <li><code>GET /dungeon/api/history</code> - 获取历史记录</li>
                        <li><code>POST /dungeon/api/history</code> - 添加历史记录</li>
                        <li><code>DELETE /dungeon/api/history</code> - 清空历史记录</li>
                        <li><code>GET /dungeon/api/history/status</code> - 获取状态信息</li>
                    </ul>
                    
                    <h4>测试步骤：</h4>
                    <ol>
                        <li>点击"获取状态信息"检查系统状态</li>
                        <li>点击"添加历史记录"或"添加多条记录"</li>
                        <li>点击"获取历史记录"验证数据保存</li>
                        <li>点击"刷新页面测试"验证持久化</li>
                        <li>点击"清空历史记录"测试清理功能</li>
                    </ol>
                    
                    <div id="api-response" class="api-response">等待API调用...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="web/js/uikit.min.js"></script>
    <script src="web/js/uikit-icons.min.js"></script>
    <script>
        const HISTORY_API_BASE = '/dungeon/api/history';
        const MAX_HISTORY = 5;
        let imageHistory = [];
        
        function logResponse(message, data = null) {
            const responseDiv = document.getElementById('api-response');
            const timestamp = new Date().toLocaleTimeString();
            let logMessage = `[${timestamp}] ${message}`;
            
            if (data) {
                logMessage += '\n' + JSON.stringify(data, null, 2);
            }
            
            responseDiv.textContent = logMessage + '\n\n' + responseDiv.textContent;
        }
        
        function showStatus(message, details = '') {
            const statusDiv = document.getElementById('status-display');
            const detailsDiv = document.getElementById('status-details');
            const errorDiv = document.getElementById('error-display');
            
            statusDiv.style.display = 'block';
            errorDiv.style.display = 'none';
            detailsDiv.textContent = details;
        }
        
        function showError(message, details = '') {
            const statusDiv = document.getElementById('status-display');
            const errorDiv = document.getElementById('error-display');
            const detailsDiv = document.getElementById('error-details');
            
            statusDiv.style.display = 'none';
            errorDiv.style.display = 'block';
            detailsDiv.textContent = details;
        }
        
        // 生成测试图片
        function generateTestImageUrl(index) {
            const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8', 'DDA0DD', 'F0E68C', 'FFB6C1'];
            const color = colors[index % colors.length];
            return `data:image/svg+xml;base64,${btoa(`
                <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#${color}"/>
                    <text x="50%" y="40%" font-family="Arial" font-size="16" fill="white" text-anchor="middle" dy=".3em">
                        测试图片 ${index + 1}
                    </text>
                    <text x="50%" y="60%" font-family="Arial" font-size="12" fill="white" text-anchor="middle" dy=".3em">
                        ${new Date().toLocaleTimeString()}
                    </text>
                </svg>
            `)}`;
        }
        
        // API测试函数
        async function testGetHistory() {
            try {
                logResponse('正在获取历史记录...');
                const response = await fetch(HISTORY_API_BASE);
                const result = await response.json();
                
                if (result.success) {
                    imageHistory = result.data.map(item => ({
                        ...item,
                        timestamp: new Date(item.timestamp)
                    }));
                    
                    updateHistoryDisplay();
                    showStatus(`成功获取 ${result.count} 条历史记录`);
                    logResponse('获取历史记录成功', result);
                } else {
                    showError('获取历史记录失败', result.error);
                    logResponse('获取历史记录失败', result);
                }
            } catch (error) {
                showError('API调用失败', error.message);
                logResponse('API调用失败', { error: error.message });
            }
        }
        
        async function testAddHistory() {
            try {
                const testData = {
                    imageUrl: generateTestImageUrl(imageHistory.length),
                    seed: Math.floor(Math.random() * 999999).toString(),
                    futureTech: ['测试科技A', '测试科技B'],
                    futureCity: ['测试城市A', '测试城市B'],
                    atmosphere: ['测试氛围'],
                    imagePath: `/test/image_${Date.now()}.png`
                };
                
                logResponse('正在添加历史记录...', testData);
                
                const response = await fetch(HISTORY_API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('成功添加历史记录');
                    logResponse('添加历史记录成功', result);
                    // 重新获取历史记录
                    await testGetHistory();
                } else {
                    showError('添加历史记录失败', result.error);
                    logResponse('添加历史记录失败', result);
                }
            } catch (error) {
                showError('API调用失败', error.message);
                logResponse('API调用失败', { error: error.message });
            }
        }
        
        async function testGetStatus() {
            try {
                logResponse('正在获取状态信息...');
                const response = await fetch(HISTORY_API_BASE + '/status');
                const result = await response.json();
                
                if (result.success) {
                    const status = result.data;
                    showStatus('系统状态正常', `文件存在: ${status.file_exists}, 记录数量: ${status.count}/${status.max_count}, 文件大小: ${status.file_size} 字节`);
                    logResponse('获取状态信息成功', result);
                } else {
                    showError('获取状态信息失败', result.error);
                    logResponse('获取状态信息失败', result);
                }
            } catch (error) {
                showError('API调用失败', error.message);
                logResponse('API调用失败', { error: error.message });
            }
        }
        
        async function testClearHistory() {
            if (!confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
                return;
            }
            
            try {
                logResponse('正在清空历史记录...');
                const response = await fetch(HISTORY_API_BASE, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    imageHistory = [];
                    updateHistoryDisplay();
                    showStatus('成功清空历史记录');
                    logResponse('清空历史记录成功', result);
                } else {
                    showError('清空历史记录失败', result.error);
                    logResponse('清空历史记录失败', result);
                }
            } catch (error) {
                showError('API调用失败', error.message);
                logResponse('API调用失败', { error: error.message });
            }
        }
        
        async function addMultipleRecords() {
            for (let i = 0; i < 3; i++) {
                await testAddHistory();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
        
        // 更新历史显示
        function updateHistoryDisplay() {
            const historyImagesElement = document.getElementById('history-images');
            if (!historyImagesElement) return;
            
            historyImagesElement.innerHTML = '';
            
            const historyTitle = document.querySelector('.history-header h4');
            if (historyTitle) {
                historyTitle.textContent = `历史图片 (${imageHistory.length}/${MAX_HISTORY})`;
            }
            
            if (imageHistory.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'history-empty-message';
                emptyMessage.innerHTML = `
                    <div style="text-align: center; color: #aaa; padding: 20px;">
                        <span uk-icon="icon: image; ratio: 2"></span>
                        <p style="margin-top: 10px;">暂无历史图片</p>
                        <p style="font-size: 0.8rem;">点击"添加历史记录"开始测试</p>
                    </div>
                `;
                historyImagesElement.appendChild(emptyMessage);
                return;
            }
            
            imageHistory.forEach(item => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-image-item';
                
                const img = document.createElement('img');
                img.src = item.imageUrl;
                img.alt = '历史图片';
                img.title = '点击查看大图';
                
                const info = document.createElement('div');
                info.className = 'history-image-info';
                info.textContent = `种子: ${item.seed}`;
                
                const time = document.createElement('div');
                time.className = 'history-image-time';
                
                const now = new Date();
                const diffMs = now - item.timestamp;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                
                if (diffMins < 1) {
                    time.textContent = '刚刚';
                } else if (diffMins < 60) {
                    time.textContent = `${diffMins}分钟前`;
                } else if (diffHours < 24) {
                    time.textContent = `${diffHours}小时前`;
                } else {
                    time.textContent = item.timestamp.toLocaleDateString();
                }
                
                historyItem.appendChild(img);
                historyItem.appendChild(info);
                historyItem.appendChild(time);
                historyImagesElement.appendChild(historyItem);
            });
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定清理按钮
            const clearBtn = document.getElementById('clear-history');
            if (clearBtn) {
                clearBtn.addEventListener('click', testClearHistory);
            }
            
            // 自动获取状态和历史记录
            setTimeout(async () => {
                await testGetStatus();
                await testGetHistory();
            }, 500);
        });
    </script>
</body>
</html>
