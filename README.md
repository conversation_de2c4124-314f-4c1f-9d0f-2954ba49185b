# 未来城市生成器
基于ComfyUI的未来城市AI图像生成插件。

**重要提示：** 这是一个基于ComfyUI的Web应用，用于生成未来城市场景图像。用户可以通过选择不同的未来科技元素、城市特征和氛围光照来创建独特的未来城市图像。

![未来城市生成器](./comfy_dungeon.jpg)

## 安装

下载或git clone此仓库到ComfyUI的`custom_nodes`目录中。然后可以通过URL访问：`http://[comfy地址]:[comfy端口]/dungeon`。例如：`http://127.0.0.1:8188/dungeon`

此扩展需要以下模型：

- [FLUX.1 DiT模型](https://huggingface.co/nunchaku-ai/flux.1-dev) (svdq-int4_r32-flux.1-dev.safetensors)
- [FLUX.1 LoRA](https://huggingface.co/nunchaku-ai/flux.1-lora) (F.1未来都市_TWO.safetensors)
- [CLIP文本编码器](https://huggingface.co/nunchaku-ai/flux.1-text-encoders) (clip_l.safetensors, awq-int4-flux.1-t5xxl.safetensors)
- [VAE](https://huggingface.co/nunchaku-ai/flux.1-vae) (ae.safetensors)

还需要以下ComfyUI扩展：
- [ComfyUI Essentials](https://github.com/cubiq/ComfyUI_essentials)
- [Nunchaku FLUX.1](https://github.com/nunchaku-ai/ComfyUI-Nunchaku-FLUX.1)
- [Qwen AI](https://github.com/your-qwen-extension) (用于文本处理)

## 功能特性

### 📸 历史图片管理
- 右侧面板显示最近生成的3张图片
- 点击历史图片可查看大图和详细信息
- 显示生成参数（种子值、选择元素、生成时间）
- 响应式设计，移动端自动隐藏历史面板
- 美观的UI设计和交互效果

### 🏙️ 未来科技选择
- 碳中和工厂
- 飞行出租车
- 磁悬浮列车
- 屋顶花园
- 快递无人机
- 互动喷泉
- 智能社区
- 摩天农场
- 自动驾驶巴士
- 生态住宅
- 太阳能屋顶
- 风力发电塔
- 智能路灯
- 回收机器人
- 雨水花园
- 城市森林
- 悬浮监控球
- 磁悬浮滑板
- 全息政务厅
- 医疗传送舱

### 🌆 未来城市选择
- 零碳城市
- 空中出行
- 地面交通
- 城市绿化
- 无人机配送
- 智慧广场
- 智慧居民区
- 垂直农场
- 智能交通
- 绿色建筑
- 太阳能板
- 风力发电
- 智能照明
- 垃圾分类
- 雨水收集
- 生态公园
- 智能监控
- 共享出行
- 数字政务
- 智慧医疗

### 🌟 氛围光照选择
- 温暖阳光
- 柔和月光
- 霓虹灯光
- 黄昏余晖
- 晨光熹微
- 神秘阴影
- 梦幻光晕
- 科技蓝光
- 金色夕阳
- 柔和阴影
- 强烈对比
- 柔和漫射
- 深邃黑暗
- 明亮通透
- 朦胧雾气
- 闪烁星光
- 温暖烛光
- 冷峻白光
- 柔和暖光
- 神秘紫光

## 使用方法

1. 在左侧面板中选择任意数量的未来科技元素
2. 选择任意数量的未来城市特征
3. 选择任意数量的氛围光照效果
4. 点击"生成未来城市"按钮
5. 等待AI生成完成，查看结果
6. 生成的图片会自动添加到右侧历史面板
7. 点击历史图片可查看大图和生成参数详情

## 从网络访问未来城市生成器

使用`--listen 0.0.0.0 --enable-cors-header '*'`选项运行ComfyUI，可以让您从本地网络中的任何设备运行应用程序。

**注意！** 这可能会将您的ComfyUI安装暴露给整个网络和/或互联网，如果运行ComfyUI的PC允许来自外部的传入连接。通常情况并非如此，因为大多数家庭路由器不允许来自外部的直接连接，但您需要知道您在做什么。

## 技术架构

- **前端**: 基于HTML/CSS/JavaScript的Web界面
- **后端**: 集成ComfyUI的API系统
- **AI模型**: 使用FLUX.1 DiT模型进行图像生成
- **文本处理**: 集成Qwen AI进行提示词优化
- **工作流**: 使用预定义的ComfyUI工作流JSON文件

## 生成流程

1. 用户选择未来科技、城市特征和氛围光照
2. 系统将选择转换为工作流参数
3. 调用Qwen AI优化提示词描述
4. 使用FLUX.1模型生成未来城市图像
5. 返回生成的图像结果

## 未来发展方向

基于用户反馈和兴趣，以下是我们正在考虑的改进：

- 扩展未来科技和城市特征选项
- 添加更多场景类型（赛博朋克、蒸汽朋克、后启示录等）
- 添加图像编辑功能（局部重绘、风格转换等）
- 支持批量生成和图像变体
- 添加更多光照和天气效果
- 支持自定义提示词输入
- 支持不同分辨率和宽高比
- 添加预设模板功能
- 历史图片持久化存储
- 图片分享和导出功能

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交问题报告和功能请求。如果您想贡献代码，请提交拉取请求。

## 联系方式

如有问题或建议，请在GitHub上提交issue或联系开发团队。
